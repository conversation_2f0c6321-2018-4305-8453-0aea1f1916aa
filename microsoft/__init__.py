import base64
from os import Path<PERSON><PERSON>
from pathlib import Path
from typing import Any
from flask import Blueprint
from sqlalchemy import select
import requests

from teslatoolbox.utils.logger import logger
from teslatoolbox.db import OAuthToken, OauthProvider, get_db_session
from microsoft.oauth2 import get_mail_bearer_token, oauth2_server, refresh_tokens
from microsoft.cid import possible_cids, get_cid_attachment

microsoft_blueprint = Blueprint('microsoftgraph', __name__)

microsoft_blueprint.register_blueprint(oauth2_server, url_prefix='/oauth2')


def send_mail(
    from_address,
    to_address,
    subject,
    *,
    reply_to: str = None,
    body: str = None,
    body_html_file: PathLike = None,
    body_preview: str = None,
    pdf_attachments: dict[str, PathLike] = None,
    raw_attachments: list[dict[str, Any]] = None,
):
    if body is None and body_html_file is None:
        logger.error('No body provided for mail from {from_address} to {to_address} with subject {subject}')
        return False
    if body is not None and body_html_file is not None:
        logger.error('Both body text and body html file provided for mail from {from_address} to {to_address} with subject {subject}')
        return False
    if body_html_file is not None:
        with open(body_html_file, 'r') as f:
            body_html = f.read()
    else:
        body_html = body

    assert body_html is not None

    token = get_mail_bearer_token(from_address)
    if token is None:
        logger.error(f'No token found for {from_address}')

    payload = {
        'message': {
            'subject': subject,
            'body': {
                'contentType': 'HTML',
                'content': body_html,
            },
            'toRecipients': [{'emailAddress': {'address': to_address}}],
        },
    }

    if reply_to is not None:
        payload['message']['replyTo'] = {'emailAddress': {'address': reply_to}}
    if body_preview is not None:
        payload['message']['bodyPreview'] = body_preview

    payload_attachments: list[dict[str, Any]] = []
    if raw_attachments is not None:
        payload_attachments.extend(raw_attachments)
    if pdf_attachments is not None:
        for attachment_name, attachment_path in pdf_attachments.items():
            with open(attachment_path, 'rb') as attachment_file:
                attachment_data = base64.b64encode(attachment_file.read()).decode('utf-8')
            payload_attachments.append(
                {
                    '@odata.type': '#microsoft.graph.fileAttachment',
                    'name': attachment_name,
                    'contentType': 'application/pdf',
                    'contentBytes': attachment_data,
                }
            )
    for cid in possible_cids:
        if body_html.find(f'cid:{cid}') != -1:
            attachment = get_cid_attachment(cid)
            payload_attachments.append(attachment)
    if len(payload_attachments) > 0:
        payload['message']['attachments'] = payload_attachments

    response = requests.post(
        'https://graph.microsoft.com/v1.0/me/sendMail',
        headers={
            'authorization': f'Bearer {token}',
            'content-type': 'application/json',
        },
        json=payload,
    )

    if response.status_code != 202:
        logger.error(f'Failed to send email to {to_address}: {response.status_code}, {response.text}')
        return False
    return True


if __name__ == '__main__':
    refresh_tokens()
    print(
        send_mail(
            from_address='<EMAIL>',
            to_address='<EMAIL>',
            subject='Your battery diagnostic report',
            body_html_file=Path('teslatoolbox/static/mail/free_report.html'),
            pdf_attachments={'report.pdf': Path('teslatoolbox/static/sample_report.pdf')},
        )
    )
