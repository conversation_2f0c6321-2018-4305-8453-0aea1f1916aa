import time
import urllib.parse
from hashlib import sha256
from random import randbytes

import requests
from flask import Blueprint, jsonify, redirect, request
from sqlalchemy import select

from teslatoolbox.db import Oauth<PERSON><PERSON>ider, OAuthToken, get_db_session
from teslatoolbox.utils.getenv import env
from teslatoolbox.utils.logger import logger

oauth2_server = Blueprint('microsoft_oauth2', __name__)

TENANT = 'common'
APP_ID = '90ab6d26-b6d4-42ca-9e9b-7bd28f59992a'
CLIENT_SECRET = '****************************************'
CALLBACK_URL = f'https://ee.rc5.be{":8002" if env == "development" else ""}/microsoft/oauth2/callback'
SCOPE = 'offline_access user.read mail.read mail.send'
STATE_SALT = 'cyS0K4UwLo5sztIqS#pm6'


@oauth2_server.route('/login')
def login():
    state = sha256(f'{request.remote_addr}{request.user_agent}{STATE_SALT}'.encode()).hexdigest()[:16]
    url = f'https://login.microsoftonline.com/{TENANT}/oauth2/v2.0/authorize?client_id={APP_ID}&response_type=code&redirect_uri={urllib.parse.quote(CALLBACK_URL, safe="")}&response_mode=query&scope={urllib.parse.quote(SCOPE, safe="")}&state={state}'
    return f'<a href="{url}">Login with Microsoft</a>'


@oauth2_server.route('/callback')
def callback():
    code = request.args.get('code')
    state = request.args.get('state')
    if code is None or state is None:
        return 'Missing parameters', 400
    if state != sha256(f'{request.remote_addr}{request.user_agent}{STATE_SALT}'.encode()).hexdigest()[:16]:
        return 'Invalid state', 400

    response = requests.post(
        f'https://login.microsoftonline.com/{TENANT}/oauth2/v2.0/token',
        data={
            'client_id': APP_ID,
            'scope': SCOPE,
            'code': code,
            'redirect_uri': CALLBACK_URL,
            'grant_type': 'authorization_code',
            'client_secret': CLIENT_SECRET,
        },
    )
    access_token_data = response.json()
    if 'error' in access_token_data:
        return jsonify(access_token_data)
    if not ('access_token' in access_token_data and 'refresh_token' in access_token_data and 'expires_in' in access_token_data):
        return jsonify({'error': 'Missing access token or refresh token or expires in'}, 400)
    access_token = access_token_data['access_token']
    refresh_token = access_token_data['refresh_token']
    expires_at = int(time.time()) + access_token_data['expires_in']

    user_data = get_user_data(access_token)
    if not user_data:
        return 'Failed to get user data', 500
    if 'mail' not in user_data:
        return 'Missing mail in user data', 500

    with get_db_session() as db:
        entry = OAuthToken(
            user_mail=user_data['mail'],
            provider=OauthProvider.MICROSOFT,
            access_token=access_token,
            refresh_token=refresh_token,
            expires_at=expires_at,
            updated_at=int(time.time()),
            raw_data=access_token_data,
            user_data=user_data,
        )
        db.add(entry)
        db.commit()
    return 'Success! You can close this page now.'


def get_user_data(bearer):
    response = requests.get('https://graph.microsoft.com/v1.0/me', headers={'Authorization': 'Bearer ' + bearer})

    try:
        return response.json()
    except requests.exceptions.JSONDecodeError:
        logger.error(f'Error on getting Microsoft user data: {response.text}')
        return None


def refresh_tokens(early_seconds=300):
    if env != 'production':
        logger.warning('Skipping token refresh in development environment')
        return
    with get_db_session() as db:
        tokens = db.scalars(select(OAuthToken).where(OAuthToken.provider == OauthProvider.MICROSOFT).where(OAuthToken.expires_at < (int(time.time()) + early_seconds)))
        for token in tokens:
            response = requests.post(
                f'https://login.microsoftonline.com/{TENANT}/oauth2/v2.0/token',
                data={
                    'client_id': APP_ID,
                    'scope': SCOPE,
                    'refresh_token': token.refresh_token,
                    'grant_type': 'refresh_token',
                    'client_secret': CLIENT_SECRET,
                },
            )

            access_token_data = response.json()
            if 'error' in access_token_data:
                logger.error(f'Error on refreshing Microsoft token for {token.user_mail}: {access_token_data}')
                continue
            if not ('access_token' in access_token_data and 'refresh_token' in access_token_data and 'expires_in' in access_token_data):
                logger.error(f'Missing access token or refresh token or expires in on refreshing Microsoft token for {token.user_mail}: {access_token_data}')
                continue
            token.access_token = access_token_data['access_token']
            token.refresh_token = access_token_data['refresh_token']
            token.expires_at = int(time.time()) + access_token_data['expires_in']
            token.updated_at = int(time.time())
            token.raw_data = access_token_data
            db.commit()
            logger.trace(f'Refreshed Microsoft token for {token.user_mail}')


def get_mail_bearer_token(mail_adress: str):
    refresh_tokens()
    with get_db_session() as db:
        token = db.scalar(select(OAuthToken).where(OAuthToken.provider == OauthProvider.MICROSOFT).where(OAuthToken.user_mail == mail_adress))
        if token is None:
            return False
        return token.access_token


if __name__ == """__main__""":
    refresh_tokens(3600)
