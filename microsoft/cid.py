import base64
from functools import cache


possible_cids = [
    'logo_white_01.png',
    'logo_with_car_01.png',
    'black_to_transparent_01.png',
]


@cache
def get_cid_attachment(cid: str):
    if cid not in possible_cids:
        raise ValueError(f'Invalid CID: {cid}')
    return {
        '@odata.type': '#microsoft.graph.fileAttachment',
        'name': cid,
        'contentType': 'image/png',
        'contentId': cid,
        'contentBytes': base64.b64encode(open(f'teslatoolbox/static/assets/{cid}', 'rb').read()).decode('utf-8'),
    }
