# Electrify Europe - Tesla Toolbox Diagnosis System

## Project Overview

This project is an automated Tesla vehicle diagnosis system that integrates with Tesla's Toolbox API to provide battery health reports and diagnostics for Tesla vehicles. The system consists of a Flask web server that handles diagnosis requests and a background worker that processes these requests through various stages including authorization, data collection, analysis, and report generation via email.

The application automates the entire workflow of:
1. Requesting owner authorization from Tesla
2. Collecting vehicle data and logs
3. Analyzing battery health and vehicle vitals
4. Generating PDF reports
5. Sending reports via Microsoft Graph API (email)

## Architecture/Components

### 1. **Flask Web Server** (`teslatoolbox/server.py`)
The main web application that provides REST API endpoints for:
- **Diagnosis Requests**: Accepts VIN and Tesla email to initiate diagnosis
- **Bearer Token Management**: Receives and manages Tesla Toolbox authentication tokens
- **Userscript Distribution**: Serves browser userscripts for Tesla Toolbox integration
- **Overview Dashboard**: Displays diagnosis status and progress
- **Report Generation**: Creates and serves PDF reports

### 2. **Background Worker** (`teslatoolbox/__main__.py`)
A continuous background process that orchestrates the diagnosis workflow through multiple stages:
- **Authorization Management**: Requests and tracks owner authorization status
- **Vehicle ID Resolution**: Retrieves Tesla vehicle IDs from VINs
- **Log Collection**: Requests and monitors vehicle log availability
- **Diagnosis Processing**: Collects vehicle vitals and generates health reports
- **Email Delivery**: Sends reports and notifications via Microsoft Graph API

### 3. **Tesla API Integration** (`teslatoolbox/tesla_api/`)
Handles all interactions with Tesla's Toolbox API:
- Bearer token management
- Authorization requests
- Vehicle data retrieval
- CAN bus signal analysis
- Log availability checking

### 4. **Microsoft Graph Integration** (`microsoft/`)
OAuth2 integration with Microsoft Graph API for:
- Email sending (reports and notifications)
- Email reading (for CID attachment extraction)
- Token refresh and management

### 5. **Database Layer** (`teslatoolbox/db.py`)
SQLite database with SQLAlchemy ORM managing:
- **Customer**: VIN, Tesla email, vehicle details, confirmation status
- **Diagnosis**: Workflow state, vitals, reports, error tracking
- **OAuthToken**: Microsoft and Teamleader OAuth tokens
- **DataStore**: Key-value storage for bearer tokens

### 6. **Services** (`teslatoolbox/services/`)
Modular service components for each workflow stage:
- `request_authorization.py`: Request owner authorization from Tesla
- `update_auth_status.py`: Check authorization approval status
- `get_vehicle_id.py`: Resolve VIN to vehicle ID
- `request_logs.py`: Request vehicle logs from Tesla
- `wait_for_logs.py`: Monitor log availability
- `diagnose.py`: Collect vitals and generate diagnosis
- `mail_report.py`: Send PDF reports via email
- `mail_wrong_details.py`: Send error notifications

### 7. **Browser Userscript** (`teslatoolbox/userscript/`)
TypeScript-based browser extension that:
- Automatically logs into Tesla Toolbox
- Extracts bearer tokens
- Sends tokens to the Flask server
- Enables automated API access

### 8. **PDF Report Generator** (`teslatoolbox/pdfreport.py`)
Generates professional PDF reports with:
- Battery health analysis
- Voltage discrepancy measurements
- Vehicle vitals and statistics
- Degradation analysis

## Ports Configuration

### Production Environment

| Component | Local Port | Public Port (NGINX) | Protocol | Description |
|-----------|-----------|---------------------|----------|-------------|
| Flask/Gunicorn Server | 8000 | 443 (HTTPS) | HTTP | Main production API server |
| Development Server | 8001 | 8002 (HTTPS) | HTTP | Development/testing server |
| phpLiteAdmin (Docker) | 2015 (container) | 8080 | HTTP | Database administration tool |

### Development Environment

| Component | Local Port | Public Port | Protocol | Description |
|-----------|-----------|-------------|----------|-------------|
| Flask Dev Server | 8001 | 8002 (via NGINX) | HTTP | Development server with debug mode |
| phpLiteAdmin (Docker) | 2015 (container) | 8080 | HTTP | Database administration tool |

### NGINX Port Mapping

The NGINX reverse proxy configuration (`/etc/nginx/sites-available/flask_app`) maps:

**Production (HTTPS - Port 443):**
- Public: `https://ee.rc5.be` → Local: `http://127.0.0.1:8000` (Gunicorn)
- Handles all production traffic with SSL/TLS encryption

**Development (HTTPS - Port 8002):**
- Public: `https://ee.rc5.be:8002` → Local: `http://127.0.0.1:8001` (Flask dev server)
- Used for testing and development with SSL/TLS encryption

**HTTP to HTTPS Redirect (Port 80):**
- All HTTP traffic is redirected to HTTPS (Port 443)

## Starting and Stopping Services

### Flask Web Server

#### Production Mode

**Start:**
```bash
sudo systemctl start flask_app.service
```

**Stop:**
```bash
sudo systemctl stop flask_app.service
```

**Restart:**
```bash
sudo systemctl restart flask_app.service
```

**Reload (graceful restart):**
```bash
sudo systemctl reload flask_app.service
# OR use the helper script:
./infra/scripts/reload-gunicorn.sh
```

**Check Status:**
```bash
sudo systemctl status flask_app.service
```

**View Logs:**
```bash
journalctl -u flask_app.service -f
# OR use the helper script:
./infra/scripts/flask-logs.sh
```

#### Development Mode

**Start:**
```bash
./infra/scripts/dev-server.sh
# OR manually:
cd /home/<USER>/code
APP_ENV="development" python -m teslatoolbox.server
```

**Stop:**
- Press `Ctrl+C` in the terminal running the dev server

### Background Worker (Main Process)

#### Production Mode

**Start:**
```bash
./infra/scripts/prod-main.sh
# OR manually:
APP_ENV="production" pipenv run python -m teslatoolbox
```

**Stop:**
- Press `Ctrl+C` in the terminal running the worker
- Or kill the process: `pkill -f "python -m teslatoolbox"`

**Note:** This is typically run in a screen/tmux session or should be set up as a systemd service for production.

#### Development Mode

**Start:**
```bash
cd /home/<USER>/code
pipenv run python -m teslatoolbox
# OR
python -m teslatoolbox
```

**Stop:**
- Press `Ctrl+C` in the terminal

### NGINX

**Reload Configuration:**
```bash
sudo service nginx reload
# OR use the helper script:
./infra/scripts/reload-nginx.sh
```

**Restart:**
```bash
sudo service nginx restart
```

**Check Status:**
```bash
sudo service nginx status
```

### Database Administration (phpLiteAdmin)

**Start:**
```bash
./infra/scripts/php-lite-admin.sh
```
This starts a Docker container with phpLiteAdmin accessible at `http://localhost:8080`

**Stop:**
```bash
docker stop websql
```

### Userscript Build

**Build Once:**
```bash
cd teslatoolbox/userscript
npm run build
```

**Watch Mode (auto-rebuild on changes):**
```bash
cd teslatoolbox/userscript
npm run watch
```

## Scripts

All utility scripts are located in the `infra/scripts/` directory.

### `infra/scripts/install.sh`
**Purpose:** Initial system setup and dependency installation

**What it does:**
- Installs system dependencies (libxml2, libxslt, libffi, libcairo, libpango)
- Installs SQLite3
- Installs npm
- Installs Node.js dependencies for userscript
- Installs Python dependencies via Pipenv

**When to use:** First-time setup or after system reinstall

**Usage:**
```bash
./infra/scripts/install.sh
```

### `infra/scripts/prod-server.sh`
**Purpose:** Start the Flask web server in production mode with Gunicorn

**What it does:**
- Sets `APP_ENV=production`
- Starts Gunicorn with 4 workers
- Binds to `0.0.0.0:8000`

**When to use:** Manually starting the production server (normally handled by systemd)

**Usage:**
```bash
./infra/scripts/prod-server.sh
```

### `infra/scripts/prod-main.sh`
**Purpose:** Start the background worker in production mode

**What it does:**
- Sets `APP_ENV=production`
- Runs the main diagnosis workflow loop

**When to use:** Starting the background worker process

**Usage:**
```bash
./infra/scripts/prod-main.sh
```

### `infra/scripts/dev-server.sh`
**Purpose:** Start the Flask web server in development mode

**What it does:**
- Sets `APP_ENV=development`
- Starts Flask development server with debug mode
- Binds to port 8001

**When to use:** Local development and testing

**Usage:**
```bash
./infra/scripts/dev-server.sh
```

### `infra/scripts/backup-db.sh`
**Purpose:** Create a backup of the production database

**What it does:**
- Creates `backup.db` from `database.db`
- Creates `dev.db` from `backup.db`
- Uses SQLite's `.backup` command with 10-second timeout

**When to use:** Before major changes or regular backups

**Usage:**
```bash
./infra/scripts/backup-db.sh
```

### `infra/scripts/copy-db-to-dev.sh`
**Purpose:** Copy production database to development database

**What it does:**
- Creates `dev.db` from `database.db`
- Useful for testing with production data

**When to use:** Syncing production data to development environment

**Usage:**
```bash
./infra/scripts/copy-db-to-dev.sh
```

### `infra/scripts/reload-gunicorn.sh`
**Purpose:** Gracefully reload the Flask application without downtime

**What it does:**
- Sends reload signal to `flask_app.service`
- Workers restart gracefully

**When to use:** After code changes in production

**Usage:**
```bash
./infra/scripts/reload-gunicorn.sh
```

### `infra/scripts/reload-nginx.sh`
**Purpose:** Reload NGINX configuration

**What it does:**
- Reloads NGINX without dropping connections

**When to use:** After NGINX configuration changes

**Usage:**
```bash
./infra/scripts/reload-nginx.sh
```

### `infra/scripts/flask-logs.sh`
**Purpose:** View Flask application logs in real-time

**What it does:**
- Follows systemd journal for `flask_app.service`

**When to use:** Debugging or monitoring production server

**Usage:**
```bash
./infra/scripts/flask-logs.sh
```

### `infra/scripts/php-lite-admin.sh`
**Purpose:** Start phpLiteAdmin for database management

**What it does:**
- Starts Docker container with phpLiteAdmin
- Mounts database directory
- Exposes on port 8080

**When to use:** Database inspection and management

**Usage:**
```bash
./infra/scripts/php-lite-admin.sh
```

## Prerequisites/Dependencies

### System Requirements
- **OS:** Linux (Ubuntu/Debian recommended)
- **Python:** 3.12.2
- **Node.js:** 18+ (for userscript build)
- **Docker:** For phpLiteAdmin (optional)

### System Packages
```bash
sudo apt install -y libxml2-dev libxslt-dev libffi-dev libcairo2-dev libpango1.0-dev
sudo apt install -y sqlite3
sudo apt install -y npm
```

### Python Dependencies (via Pipenv)
- **Flask** (with async support) - Web framework
- **Gunicorn** - WSGI HTTP server
- **SQLAlchemy** - ORM and database toolkit
- **Requests** - HTTP library for API calls
- **Playwright** - Browser automation
- **tf-playwright-stealth** - Stealth mode for Playwright
- **Flask-CORS** - Cross-origin resource sharing
- **Loguru** - Logging library
- **2captcha-python** - CAPTCHA solving service

### Node.js Dependencies (for userscript)
- **TypeScript** - Type-safe JavaScript
- **esbuild** - Fast JavaScript bundler
- **javascript-obfuscator** - Code obfuscation
- **nodemon** - Auto-restart on file changes
- **totp-generator** - TOTP authentication

### External Services
- **Tesla Toolbox API** - Vehicle data and diagnostics
- **Microsoft Graph API** - Email sending (OAuth2 required)
- **2Captcha** - CAPTCHA solving service
- **Let's Encrypt** - SSL/TLS certificates (via Certbot)

### Installation

1. **Clone the repository:**
```bash
git clone https://gitlab.com/RobbertC5/electrify_europe.git
cd electrify_europe
```

2. **Run the installation script:**
```bash
./infra/scripts/install.sh
```

3. **Install Playwright browsers:**
```bash
pipenv run playwright install
```

4. **Build the userscript:**
```bash
cd teslatoolbox/userscript
npm run build
cd ../..
```

5. **Set up the database:**
```bash
pipenv run python -c "from teslatoolbox.db import init_models; init_models()"
```

6. **Configure systemd service (production):**
```bash
sudo cp infra/systemd/flask_app.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable flask_app.service
sudo systemctl start flask_app.service
```

7. **Configure NGINX:**
- Ensure NGINX is installed
- SSL certificates should be configured via Certbot
- The NGINX configuration reference is at `infra/nginx_flask_app.conf`
- Active configuration is at `/etc/nginx/sites-available/flask_app`

## Environment Variables

- **APP_ENV**: Set to `production` or `development` (default: `development`)
  - Controls database selection (database.db vs dev.db)
  - Enables/disables debug mode
  - Affects logging verbosity

## Database

The application uses SQLite with Write-Ahead Logging (WAL) mode for better concurrency.

**Production database:** `database/database.db`
**Development database:** `database/dev.db`

## Workflow States

Diagnoses progress through the following states:
1. `INIT` → Initial state
2. `REQUESTING_AUTHORIZATION` → Requesting owner authorization
3. `AUTHORIZATION_PENDING` → Waiting for owner approval
4. `GETTING_VEHICLE_ID` → Resolving VIN to vehicle ID
5. `REQUESTING_LOGS` → Requesting vehicle logs
6. `WAITING_FOR_LOGS` → Waiting for logs to be available
7. `WAKING_VEHICLE` → Waking up the vehicle
8. `DIAGNOSING` → Collecting vitals and generating report
9. `SENDING_REPORT` → Sending PDF report via email
10. `DONE` → Completed successfully
11. `FAILED` → Failed with error

## Security Notes

- Bearer tokens are stored in the database and should be kept secure
- OAuth2 credentials are hardcoded in the source (should be moved to environment variables)
- API tokens and secrets should be rotated regularly
- The system uses HTTPS with Let's Encrypt certificates
- Database backups should be performed regularly

## Troubleshooting

**Flask server not starting:**
- Check logs: `./scripts/flask-logs.sh`
- Verify port 8000 is not in use: `sudo lsof -i :8000`
- Check systemd status: `sudo systemctl status flask_app.service`

**Background worker stuck:**
- Check if bearer token is available
- Verify Tesla Toolbox API access
- Check database for diagnosis states
- Review application logs

**Database locked errors:**
- Ensure WAL mode is enabled
- Check for long-running transactions
- Verify file permissions on database directory

**NGINX errors:**
- Check configuration: `sudo nginx -t`
- Review error logs: `sudo tail -f /var/log/nginx/error.log`
- Verify SSL certificates are valid

## License

This project is proprietary software for Electrify Europe.

## Contact

For issues or questions, contact the development team.

