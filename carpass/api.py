import time
import urllib.parse
from hashlib import sha256
from random import randbytes

import requests
from flask import Blueprint, jsonify, redirect, request
from sqlalchemy import select

from teslatoolbox.db import Customer, CustomerConfirmation, Diagnosis, DiagnosisState, DiagnosisType, OAuthToken, get_db_session, may_retry
from teslatoolbox.utils.getenv import env
from teslatoolbox.utils.logger import logger
from teslatoolbox.tesla_api.tesla_types import CleanVehicleSummary

api_server = Blueprint('carpass_api', __name__)

CLIENT_BEARER = "DjXMdupJztzVdwskBgvK2lzitaVQf1ro6tF6M4uY4b0Ag0fEB5MmAMJ5spx9XyR0"


@api_server.route('/state-of-health-report')
def state_of_health_report():
    # check if header: Authorization: Bearer CLIENT_BEARER is present
    if request.headers.get('Authorization') != f'Bearer {CLIENT_BEARER}':
        return jsonify({'error': 'Unauthorized'}), 401
    
    vin = request.args.get('vin')
    if vin is None:
        return jsonify({'error': 'Missing VIN'}), 400
    
    # get from database
    with get_db_session() as db:
        diagnosis = db.scalar(select(Diagnosis).where(Diagnosis.customer.vin == vin).where(Diagnosis.state == DiagnosisState.DONE).order_by(Diagnosis.id.desc()).limit(1))
        if diagnosis is None:
            return jsonify({'error': 'No report found'}), 404
        vitals: CleanVehicleSummary = diagnosis.vitals
        
        return jsonify({
            'stateOfHealth': None,
            'electricRangeNew': None,
            'electricRangeActual': None,
            'date': time.strftime('%Y-%m-%d', time.localtime(diagnosis.vitals['time'])),
            'odometerReading': vitals['odometer'],
            'publicUrl': f"https://ee.rc5.be/report?id={diagnosis.id}",
        })
