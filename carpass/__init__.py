import base64
from os import PathLike
from pathlib import Path
from typing import Any
from flask import Blueprint
from sqlalchemy import select
import requests

from teslatoolbox.utils.logger import logger
from teslatoolbox.db import OAuthToken, OauthProvider, get_db_session
from carpass.api import api_server

carpass_blueprint = Blueprint('carpass', __name__)

carpass_blueprint.register_blueprint(api_server, url_prefix='/api/v1')
