# Systemd Service Files

This directory contains systemd service files for the Electrify Europe Tesla Toolbox application.

## Available Services

### flask_app.service
**Description:** Flask web server with Gun<PERSON>

**What it does:**
- Runs the Flask web application using Gunicorn WSGI server
- Binds to port 8000 (proxied by NGINX)
- Runs with 4 worker processes
- Automatically restarts on failure
- Runs as user `ubuntu`

**Installation:**
```bash
sudo cp flask_app.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable flask_app.service
sudo systemctl start flask_app.service
```

**Management:**
```bash
# Start
sudo systemctl start flask_app.service

# Stop
sudo systemctl stop flask_app.service

# Restart
sudo systemctl restart flask_app.service

# Reload (graceful)
sudo systemctl reload flask_app.service

# Status
sudo systemctl status flask_app.service

# View logs
journalctl -u flask_app.service -f
```

## Recommended: Background Worker Service

Currently, the background worker (`teslatoolbox/__main__.py`) is not set up as a systemd service. It's recommended to create one for production use.

### Example: teslatoolbox_worker.service

Create a file `teslatoolbox_worker.service` with the following content:

```ini
[Unit]
Description=Tesla Toolbox Background Worker
After=network.target flask_app.service

[Service]
Environment="APP_ENV=production"
User=ubuntu
WorkingDirectory=/home/<USER>/code
ExecStart=/usr/bin/pipenv run python -m teslatoolbox
Type=simple
Restart=always
RemainAfterExit=no
RestartSec=10s

[Install]
WantedBy=multi-user.target
```

**Installation:**
```bash
sudo cp teslatoolbox_worker.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable teslatoolbox_worker.service
sudo systemctl start teslatoolbox_worker.service
```

## Notes

- All services run as the `ubuntu` user
- Working directory is `/home/<USER>/code`
- Services use Pipenv for Python environment management
- Services are configured to restart automatically on failure
- The `APP_ENV=production` environment variable is set for all services

## Updating Services

After modifying any service file:

1. Copy the updated file to `/etc/systemd/system/`
2. Reload systemd: `sudo systemctl daemon-reload`
3. Restart the service: `sudo systemctl restart <service-name>`

## Logs

View logs for any service:
```bash
journalctl -u <service-name> -f
```

View logs with more context:
```bash
journalctl -u <service-name> -n 100 --no-pager
```

View logs since a specific time:
```bash
journalctl -u <service-name> --since "1 hour ago"
```

