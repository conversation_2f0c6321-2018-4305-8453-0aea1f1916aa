# Infrastructure Configuration

This directory contains all infrastructure-related files for the Electrify Europe Tesla Toolbox project.

## Directory Structure

```
infra/
├── README.md                      # This file
├── nginx_flask_app.conf           # NGINX reverse proxy configuration
├── scripts/                       # Utility scripts
│   ├── backup-db.sh               # Database backup
│   ├── copy-db-to-dev.sh          # Copy production DB to dev
│   ├── dev-server.sh              # Start development server
│   ├── flask-logs.sh              # View Flask logs
│   ├── install.sh                 # System installation
│   ├── php-lite-admin.sh          # Start database admin
│   ├── prod-main.sh               # Start production worker
│   ├── prod-server.sh             # Start production server
│   ├── reload-gunicorn.sh         # Reload Flask app
│   └── reload-nginx.sh            # Reload NGINX
└── systemd/                       # Systemd service files
    ├── README.md                  # Systemd documentation
    ├── flask_app.service          # Flask web server service
    └── teslatoolbox_worker.service # Background worker service
```

## Contents

### NGINX Configuration

**File:** `nginx_flask_app.conf`

NGINX reverse proxy configuration for the Flask application. This file is a copy of `/etc/nginx/sites-available/flask_app`.

**Configuration:**
- Production: Port 443 (HTTPS) → Port 8000 (Gunicorn)
- Development: Port 8002 (HTTPS) → Port 8001 (Flask dev)
- SSL/TLS: Let's Encrypt certificates
- HTTP to HTTPS redirect

**To update NGINX:**
```bash
sudo cp infra/nginx_flask_app.conf /etc/nginx/sites-available/flask_app
sudo nginx -t
sudo systemctl reload nginx
```

### Scripts

**Directory:** `scripts/`

Utility scripts for managing the application. See the main README.md for detailed documentation of each script.

**Quick Reference:**
- `install.sh` - Initial system setup
- `prod-server.sh` - Start production Flask server
- `prod-main.sh` - Start production background worker
- `dev-server.sh` - Start development server
- `backup-db.sh` - Backup database
- `copy-db-to-dev.sh` - Copy DB to development
- `reload-gunicorn.sh` - Reload Flask app
- `reload-nginx.sh` - Reload NGINX
- `flask-logs.sh` - View Flask logs
- `php-lite-admin.sh` - Start database admin

**Usage:**
```bash
# From project root
./infra/scripts/script-name.sh

# Or from infra directory
cd infra/scripts
./script-name.sh
```

### Systemd Services

**Directory:** `systemd/`

Systemd service files for production deployment. See `systemd/README.md` for detailed documentation.

**Available Services:**
- `flask_app.service` - Flask web server with Gunicorn
- `teslatoolbox_worker.service` - Background worker process

**Installation:**
```bash
sudo cp infra/systemd/flask_app.service /etc/systemd/system/
sudo cp infra/systemd/teslatoolbox_worker.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable flask_app.service teslatoolbox_worker.service
sudo systemctl start flask_app.service teslatoolbox_worker.service
```

## Quick Start

### Development

```bash
# Start development server
./infra/scripts/dev-server.sh

# In another terminal, start background worker
pipenv run python -m teslatoolbox
```

### Production

```bash
# Install services
sudo cp infra/systemd/*.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable flask_app.service teslatoolbox_worker.service

# Start services
sudo systemctl start flask_app.service teslatoolbox_worker.service

# Check status
sudo systemctl status flask_app.service teslatoolbox_worker.service
```

## Updating Configuration

### After Code Changes

**Production:**
```bash
./infra/scripts/reload-gunicorn.sh
sudo systemctl restart teslatoolbox_worker.service
```

**Development:**
- Flask dev server auto-reloads
- Background worker needs manual restart

### After NGINX Changes

```bash
# Edit configuration
sudo nano /etc/nginx/sites-available/flask_app

# Test configuration
sudo nginx -t

# Reload NGINX
./infra/scripts/reload-nginx.sh

# Update local copy
sudo cp /etc/nginx/sites-available/flask_app infra/nginx_flask_app.conf
```

### After Systemd Changes

```bash
# Edit service file
nano infra/systemd/flask_app.service

# Copy to system
sudo cp infra/systemd/flask_app.service /etc/systemd/system/

# Reload and restart
sudo systemctl daemon-reload
sudo systemctl restart flask_app.service
```

## Maintenance

### Database Backups

```bash
# Manual backup
./infra/scripts/backup-db.sh

# Automated backups (add to crontab)
0 2 * * * /home/<USER>/code/infra/scripts/backup-db.sh
```

### Log Management

```bash
# View Flask logs
./infra/scripts/flask-logs.sh

# View worker logs
journalctl -u teslatoolbox_worker.service -f

# View NGINX logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### Database Administration

```bash
# Start phpLiteAdmin
./infra/scripts/php-lite-admin.sh

# Access at http://localhost:8080
```

## Security Notes

- NGINX configuration includes SSL/TLS with Let's Encrypt
- All services run as `ubuntu` user
- Database files should have restricted permissions
- Systemd services use environment variables for configuration
- Keep NGINX and systemd configurations in sync with this directory

## Documentation

For more detailed information, see:
- Main documentation: `../README.md`
- Quick start guide: `../QUICKSTART.md`
- Architecture: `../ARCHITECTURE.md`
- Systemd services: `systemd/README.md`

## File Permissions

Ensure scripts are executable:
```bash
chmod +x infra/scripts/*.sh
```

## Version Control

When making changes:
1. Update files in `infra/` directory
2. Test changes in development
3. Deploy to production
4. Update documentation if needed
5. Commit changes to version control

## Support

For issues or questions:
- Check the main README.md
- Review QUICKSTART.md for common tasks
- Check systemd/README.md for service issues
- Review ARCHITECTURE.md for system design

