import requests
import webbrowser
from flask import Flask, request, jsonify, redirect

app = Flask(__name__)

CLIENT_ID = '10c43d40170122e3bd0d5d81464f2d4b'
CLIENT_SECRET = '72cc91765f9149053f5425d7b9838a84'
PORT = 5000


# Page to serve a new user
@app.route('/login')
def login():
    url = 'https://focus.teamleader.eu/oauth2/authorize?client_id=' + CLIENT_ID + '&response_type=code&redirect_uri=http://localhost:' + str(PORT) + '/callback'
    return redirect(url, code=302)


# Page to serve the callback
@app.route('/callback')
def callback():
    code = request.args.get('code')
    response = requests.post(
        'https://focus.teamleader.eu/oauth2/access_token',
        data={
            'code': code,
            'client_id': CLIENT_ID,
            'client_secret': CLIENT_SECRET,
            'redirect_uri': 'http://localhost:' + str(PORT) + '/callback',
            'grant_type': 'authorization_code',
        },
    )
    access_token_data = response.json()
    print(access_token_data)
    if 'errors' in access_token_data:
        return jsonify(access_token_data)
    access_token = access_token_data['access_token']
    print(access_token)

    # Get the user identity information using the access token.
    response = requests.get(
        'https://api.focus.teamleader.eu/users.me',
        headers={'Authorization': 'Bearer ' + access_token},
    )
    print(response.json())
    if 'errors' in response.json():
        return jsonify(response.json())
    user = response.json()['data']
    user['access_token_data'] = access_token_data

    # with database as db:
    #     users = db.table('teamleader_users')

    #     if users.search(Query().id == user['id']):
    #         users.update(user, Query().id == user['id'])
    #         feedback = 'User updated.'
    #     else:
    #         users.insert(user)
    #         feedback = 'User created.'

    # close page
    return (
        '<b>'
        # + feedback
        + '</b><br>Success! You can close this page now.<br>'
        + '<a href="javascript:window.close();">Close</a><script>setTimeout(function(){window.close();}, 5000);</script>'
    )


webbrowser.open('http://localhost:' + str(PORT) + '/login', new=2, autoraise=False)
app.run(port=PORT)
