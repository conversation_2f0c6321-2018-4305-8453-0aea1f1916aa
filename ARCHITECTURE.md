# System Architecture

## High-Level Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────────┐
│                          External Services                              │
├─────────────────────────────────────────────────────────────────────────┤
│  Tesla Toolbox API  │  Microsoft Graph API  │  2Captcha  │ Let's Encrypt│
└──────────┬──────────┴───────────┬────────────┴─────┬──────┴──────┬──────┘
           │                      │                  │             │
           │                      │                  │             │
┌──────────▼──────────────────────▼──────────────────▼─────────────▼───────┐
│                         Internet (HTTPS)                                 │
└──────────┬───────────────────────────────────────────────────────────────┘
           │
           │ Port 443 (HTTPS)
           │ Port 8002 (HTTPS - Dev)
           │
┌──────────▼────────────────────────────────────────────────────────────────┐
│                            NGINX Reverse Proxy                            │
│                         (SSL/TLS Termination)                             │
├───────────────────────────────────────────────────────────────────────────┤
│  • Port 443 → 127.0.0.1:8000 (Production)                                 │
│  • Port 8002 → 127.0.0.1:8001 (Development)                               │
│  • SSL Certificates: Let's Encrypt                                        │
└──────────┬─────────────────────────────────┬──────────────────────────────┘
           │                                 │
           │ Port 8000                       │ Port 8001
           │                                 │
┌──────────▼─────────────────────┐  ┌────────▼──────────────────────────────┐
│   Flask/Gunicorn (Production)  │  │   Flask Dev Server (Development)      │
│   systemd: flask_app.service   │  │   Script: infra/scripts/dev-server.sh │
│   Workers: 4                   │  │   Debug: Enabled                      │
│   Port: 8000                   │  │   Port: 8001                          │
└──────────┬─────────────────────┘  └────────┬──────────────────────────────┘
           │                                 │
           └─────────────┬───────────────────┘
                         │
                         │ Flask Application (teslatoolbox/server.py)
                         │
           ┌─────────────▼──────────────────────────────────────────┐
           │              Flask Web Server                          │
           ├────────────────────────────────────────────────────────┤
           │  Endpoints:                                            │
           │  • POST /request_diagnosis - Start new diagnosis       │
           │  • POST /bearer - Receive Tesla bearer token           │
           │  • GET /task - Userscript task polling                 │
           │  • GET /overview - Diagnosis dashboard                 │
           │  • GET /report - Generate PDF report                   │
           │  • GET /script.user.js - Serve userscript              │
           │  • GET /hello - Health check                           │
           │  • /microsoft/* - OAuth2 endpoints                     │
           └─────────────┬──────────────────────────────────────────┘
                         │
                         │ Shared Database Access
                         │
           ┌─────────────▼───────────────────────────────────────────┐
           │              SQLite Database (WAL Mode)                 │
           ├─────────────────────────────────────────────────────────┤
           │  Tables:                                                │
           │  • customer - VIN, email, vehicle details               │
           │  • diagnosis - Workflow state, vitals, reports          │
           │  • oauth_token - Microsoft/Teamleader tokens            │
           │  • datastore - Bearer tokens, key-value storage         │
           ├─────────────────────────────────────────────────────────┤
           │  Files:                                                 │
           │  • Production: database/database.db                     │
           │  • Development: database/dev.db                         │
           └─────────────┬───────────────────────────────────────────┘
                         │
                         │ Shared Database Access
                         │
           ┌─────────────▼───────────────────────────────────────────┐
           │         Background Worker (Main Loop)                   │
           │         systemd: teslatoolbox_worker.service (rec.)     │
           │         Script: infra/scripts/prod-main.sh              │
           ├─────────────────────────────────────────────────────────┤
           │  Workflow Services (254s loop):                         │
           │  1. request_authorization - Request owner auth          │
           │  2. update_auth_status - Check auth approval            │
           │  3. get_vehicle_id - Resolve VIN to vehicle ID          │
           │  4. request_logs - Request vehicle logs                 │
           │  5. wait_for_logs - Monitor log availability            │
           │  6. diagnose - Collect vitals & generate report         │
           │  7. mail_report - Send PDF via email                    │
           │  8. mail_wrong_details - Send error notifications       │
           └─────────────┬───────────────────────────────────────────┘
                         │
                         │ API Calls
                         │
           ┌─────────────▼───────────────────────────────────────────┐
           │         Tesla API Integration Layer                     │
           ├─────────────────────────────────────────────────────────┤
           │  • Bearer token management                              │
           │  • Authorization requests                               │
           │  • Vehicle ID resolution                                │
           │  • Vehicle summary (vitals)                             │
           │  • Log requests & availability                          │
           │  • CAN bus signal retrieval                             │
           │  • Vehicle wake-up                                      │
           └─────────────────────────────────────────────────────────┘


┌─────────────────────────────────────────────────────────────────────────┐
│                      Browser Userscript Component                       │
├─────────────────────────────────────────────────────────────────────────┤
│  TypeScript Userscript (Violentmonkey/Tampermonkey)                     │
│  • Runs on toolbox.tesla.com                                            │
│  • Auto-login to Tesla Toolbox                                          │
│  • Extract bearer token                                                 │
│  • Send token to Flask server                                           │
│  • Poll for tasks via /task endpoint                                    │
│                                                                         │
│  Build: npm run build (teslatoolbox/userscript/)                        │
│  Served: https://ee.rc5.be/script.user.js                               │
└─────────────────────────────────────────────────────────────────────────┘


┌─────────────────────────────────────────────────────────────────────────┐
│                      Supporting Components                              │
├─────────────────────────────────────────────────────────────────────────┤
│  • phpLiteAdmin (Docker) - Database management on port 8080             │
│  • Microsoft OAuth2 - Email sending via Graph API                       │
│  • PDF Generator - WeasyPrint-based report generation                   │
│  • Playwright - Browser automation for token extraction                 │
└─────────────────────────────────────────────────────────────────────────┘
```

## Data Flow

### 1. Diagnosis Request Flow

```
User/API Request
    │
    ▼
POST /request_diagnosis
    │
    ▼
Create Customer & Diagnosis records
    │
    ▼
State: INIT
    │
    ▼
Background Worker picks up
    │
    ▼
State: REQUESTING_AUTHORIZATION
    │
    ▼
Tesla API: Request owner authorization
    │
    ▼
State: AUTHORIZATION_PENDING
    │
    ▼
Wait for owner approval (polling)
    │
    ▼
State: GETTING_VEHICLE_ID
    │
    ▼
Tesla API: Get vehicle ID from VIN
    │
    ▼
State: REQUESTING_LOGS
    │
    ▼
Tesla API: Request vehicle logs
    │
    ▼
State: WAITING_FOR_LOGS
    │
    ▼
Poll log availability (70%+ required)
    │
    ▼
State: WAKING_VEHICLE
    │
    ▼
Tesla API: Wake up vehicle
    │
    ▼
State: DIAGNOSING
    │
    ▼
Tesla API: Get vehicle summary (vitals)
    │
    ▼
Generate measurements & report
    │
    ▼
State: SENDING_REPORT
    │
    ▼
Microsoft Graph API: Send email with PDF
    │
    ▼
State: DONE
```

### 2. Bearer Token Flow

```
User opens Tesla Toolbox in browser
    │
    ▼
Userscript auto-loads
    │
    ▼
Userscript logs in (if needed)
    │
    ▼
Userscript extracts bearer token
    │
    ▼
POST /bearer (token sent to Flask)
    │
    ▼
Token stored in database (datastore table)
    │
    ▼
Background worker uses token for API calls
    │
    ▼
Token expires → Userscript sends new token
```

## Component Interactions

### Flask Server ↔ Database
- **Read**: Check diagnosis requests, get customer info
- **Write**: Update bearer tokens, store OAuth tokens

### Background Worker ↔ Database
- **Read**: Get pending diagnoses, bearer tokens, OAuth tokens
- **Write**: Update diagnosis state, vitals, reports, errors

### Background Worker ↔ Tesla API
- **Requests**: Authorization, vehicle data, logs, signals
- **Authentication**: Bearer token from database

### Background Worker ↔ Microsoft Graph API
- **Requests**: Send emails with PDF attachments
- **Authentication**: OAuth2 tokens from database

### Userscript ↔ Flask Server
- **POST /bearer**: Send extracted bearer token
- **GET /task**: Poll for tasks to execute
- **Response**: Task instructions (e.g., send token)

## State Machine

```
INIT
  ↓
REQUESTING_AUTHORIZATION
  ↓
AUTHORIZATION_PENDING
  ↓
GETTING_VEHICLE_ID
  ↓
REQUESTING_LOGS
  ↓
WAITING_FOR_LOGS
  ↓
WAKING_VEHICLE
  ↓
DIAGNOSING
  ↓
SENDING_REPORT
  ↓
DONE

(Any state can transition to FAILED on error)
```

## Deployment Architecture

### Production Environment
- **Web Server**: Gunicorn (4 workers) on port 8000
- **Background Worker**: Python process (should be systemd service)
- **Reverse Proxy**: NGINX with SSL on port 443
- **Database**: SQLite with WAL mode
- **Environment**: APP_ENV=production

### Development Environment
- **Web Server**: Flask dev server on port 8001
- **Background Worker**: Python process (manual start)
- **Reverse Proxy**: NGINX with SSL on port 8002
- **Database**: Separate dev.db
- **Environment**: APP_ENV=development

## Security Layers

1. **HTTPS/TLS**: All external traffic encrypted (Let's Encrypt)
2. **NGINX**: Reverse proxy, SSL termination
3. **Authentication**: Bearer tokens, OAuth2
4. **Authorization**: Token-based API access
5. **Database**: Local SQLite, file permissions

## Scalability Considerations

**Current Limitations:**
- Single SQLite database (not suitable for high concurrency)
- Single background worker process
- No load balancing for workers

**Potential Improvements:**
- Migrate to PostgreSQL/MySQL for better concurrency
- Multiple worker processes with task queue (Celery/RQ)
- Redis for caching and session management
- Horizontal scaling with load balancer

## Monitoring Points

1. **Flask Server**: HTTP response times, error rates
2. **Background Worker**: Task processing times, queue depth
3. **Database**: Query performance, lock contention
4. **External APIs**: Rate limits, response times, failures
5. **System**: CPU, memory, disk I/O

## Backup Strategy

- **Database**: `infra/scripts/backup-db.sh` script (manual)
- **Recommended**: Automated daily backups via cron
- **Files**: Database files in `database/` directory
- **Retention**: Keep multiple backup versions

