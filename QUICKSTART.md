# Quick Start Guide

This guide will help you get the Electrify Europe Tesla Toolbox system up and running quickly.

## Prerequisites Check

Before starting, ensure you have:
- [ ] Ubuntu/Debian Linux system
- [ ] Python 3.12.2 installed
- [ ] Node.js 18+ installed
- [ ] Git installed
- [ ] Sudo access
- [ ] Internet connection

## Installation (First Time Setup)

### 1. <PERSON>lone the Repository
```bash
cd /home/<USER>
git clone https://gitlab.com/RobbertC5/electrify_europe.git code
cd code
```

### 2. Run Installation Script
```bash
./infra/scripts/install.sh
```

This will install:
- System dependencies (libxml2, libxslt, libffi, libcairo, libpango)
- SQLite3
- npm
- Node.js dependencies (userscript)
- Python dependencies (via Pipenv)

### 3. Install Playwright Browsers
```bash
pipenv run playwright install
```

### 4. Build the Userscript
```bash
cd teslatoolbox/userscript
npm run build
cd ../..
```

### 5. Initialize the Database
```bash
pipenv run python -c "from teslatoolbox.db import init_models; init_models()"
```

### 6. Set Up Production Services (Optional)

**Flask Web Server:**
```bash
sudo cp infra/systemd/flask_app.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable flask_app.service
sudo systemctl start flask_app.service
```

**Background Worker (Recommended):**
```bash
sudo cp infra/systemd/teslatoolbox_worker.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable teslatoolbox_worker.service
sudo systemctl start teslatoolbox_worker.service
```

### 7. Verify Installation

**Check Flask Server:**
```bash
sudo systemctl status flask_app.service
```

**Check Background Worker:**
```bash
sudo systemctl status teslatoolbox_worker.service
```

**Test Web Server:**
```bash
curl http://localhost:8000/hello
# Should return: Hello, World!
```

## Development Setup

### 1. Start Development Server
```bash
./infra/scripts/dev-server.sh
```

The server will start on port 8001 and be accessible at:
- Local: `http://localhost:8001`
- Public: `https://ee.rc5.be:8002` (via NGINX)

### 2. Start Background Worker (Separate Terminal)
```bash
pipenv run python -m teslatoolbox
```

### 3. Build Userscript in Watch Mode (Optional)
```bash
cd teslatoolbox/userscript
npm run watch
```

This will automatically rebuild the userscript when you make changes.

## Daily Operations

### Starting Services (Production)

**Start Everything:**
```bash
sudo systemctl start flask_app.service
sudo systemctl start teslatoolbox_worker.service
```

**Check Status:**
```bash
sudo systemctl status flask_app.service
sudo systemctl status teslatoolbox_worker.service
```

### Stopping Services (Production)

**Stop Everything:**
```bash
sudo systemctl stop flask_app.service
sudo systemctl stop teslatoolbox_worker.service
```

### Viewing Logs

**Flask Server Logs:**
```bash
./infra/scripts/flask-logs.sh
# OR
journalctl -u flask_app.service -f
```

**Background Worker Logs:**
```bash
journalctl -u teslatoolbox_worker.service -f
```

**NGINX Logs:**
```bash
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### Reloading After Code Changes

**Production (Graceful Reload):**
```bash
./infra/scripts/reload-gunicorn.sh
# OR
sudo systemctl reload flask_app.service
```

**Background Worker (Requires Restart):**
```bash
sudo systemctl restart teslatoolbox_worker.service
```

**Development:**
- Flask dev server auto-reloads on code changes
- Background worker needs manual restart (Ctrl+C, then restart)

## Common Tasks

### Database Management

**View Database:**
```bash
./infra/scripts/php-lite-admin.sh
```
Then open: `http://localhost:8080`

**Backup Database:**
```bash
./infra/scripts/backup-db.sh
```

**Copy Production DB to Development:**
```bash
./infra/scripts/copy-db-to-dev.sh
```

**Direct SQLite Access:**
```bash
sqlite3 database/database.db
# OR for development:
sqlite3 database/dev.db
```

### Userscript Management

**Install Userscript:**
1. Install Violentmonkey or Tampermonkey browser extension
2. Navigate to: `https://ee.rc5.be/script.user.js`
3. Click "Install"

**Update Userscript:**
1. Make changes in `teslatoolbox/userscript/src/`
2. Build: `npm run build` (or use watch mode)
3. Reload the userscript in your browser

### Testing a Diagnosis

**1. Ensure Services are Running:**
```bash
sudo systemctl status flask_app.service
sudo systemctl status teslatoolbox_worker.service
```

**2. Ensure Bearer Token is Available:**
- Open Tesla Toolbox in browser with userscript installed
- Userscript will automatically send bearer token

**3. Submit Diagnosis Request:**
```bash
curl -X POST https://ee.rc5.be/request_diagnosis \
  -H "Content-Type: application/json" \
  -d '{
    "vin": "5YJSA7E43KF343125",
    "tesla_mail": "<EMAIL>",
    "type": "free_diagnosis_light_show",
    "token": "e0y9hRo0AC730fAEvBlg7zPHa27MbE"
  }'
```

**4. Monitor Progress:**
```bash
# View overview (requires token)
curl "https://ee.rc5.be/overview?token=very_secret"

# Watch worker logs
journalctl -u teslatoolbox_worker.service -f
```

## Troubleshooting

### Flask Server Won't Start

**Check if port is in use:**
```bash
sudo lsof -i :8000
```

**Check logs:**
```bash
journalctl -u flask_app.service -n 50
```

**Verify Pipenv:**
```bash
pipenv --version
pipenv run python --version
```

### Background Worker Not Processing

**Check if bearer token exists:**
```bash
sqlite3 database/database.db "SELECT * FROM datastore WHERE key='bearer';"
```

**Check worker logs:**
```bash
journalctl -u teslatoolbox_worker.service -f
```

**Manually test worker:**
```bash
APP_ENV=production pipenv run python -m teslatoolbox
```

### Database Locked Errors

**Check WAL mode:**
```bash
sqlite3 database/database.db "PRAGMA journal_mode;"
# Should return: wal
```

**Enable WAL mode if needed:**
```bash
sqlite3 database/database.db "PRAGMA journal_mode=WAL;"
```

### NGINX Issues

**Test configuration:**
```bash
sudo nginx -t
```

**Reload configuration:**
```bash
./scripts/reload-nginx.sh
```

**Check NGINX status:**
```bash
sudo systemctl status nginx
```

### Userscript Not Working

**Check if userscript is loaded:**
- Open browser console on toolbox.tesla.com
- Look for "Start!" message

**Rebuild userscript:**
```bash
cd teslatoolbox/userscript
npm run build
```

**Check server is accessible:**
```bash
curl https://ee.rc5.be/hello
```

## Environment Variables

The system uses one main environment variable:

**APP_ENV**: Controls production vs development mode
- `production`: Uses `database/database.db`, production settings
- `development`: Uses `database/dev.db`, debug mode enabled

**Set in systemd services:**
```ini
Environment="APP_ENV=production"
```

**Set manually:**
```bash
export APP_ENV=production
# OR
APP_ENV=production pipenv run python -m teslatoolbox
```

## Next Steps

After getting the system running:

1. **Set up monitoring**: Add health checks and alerting
2. **Configure backups**: Schedule `backup-db.sh` via cron
3. **Review security**: Move secrets to environment variables
4. **Test workflow**: Submit test diagnosis and verify email delivery
5. **Documentation**: Read full README.md for detailed information

## Getting Help

- **Full Documentation**: See `README.md`
- **Architecture**: See `ARCHITECTURE.md`
- **Systemd Services**: See `systemd/README.md`
- **Scripts**: See `README.md` Scripts section

## Quick Reference

### Important URLs
- Production: `https://ee.rc5.be`
- Development: `https://ee.rc5.be:8002`
- Userscript: `https://ee.rc5.be/script.user.js`
- Overview: `https://ee.rc5.be/overview?token=very_secret`
- Database Admin: `http://localhost:8080`

### Important Ports
- 8000: Production Flask/Gunicorn
- 8001: Development Flask
- 8002: Public development (NGINX)
- 8080: phpLiteAdmin
- 443: Public production (NGINX)

### Important Files
- Database: `database/database.db` (production)
- Database: `database/dev.db` (development)
- Logs: `journalctl -u flask_app.service`
- Config: `/etc/nginx/sites-available/flask_app`

### Important Commands
```bash
# Start production
sudo systemctl start flask_app.service teslatoolbox_worker.service

# Stop production
sudo systemctl stop flask_app.service teslatoolbox_worker.service

# View logs
./infra/scripts/flask-logs.sh

# Reload after changes
./infra/scripts/reload-gunicorn.sh

# Backup database
./infra/scripts/backup-db.sh
```

