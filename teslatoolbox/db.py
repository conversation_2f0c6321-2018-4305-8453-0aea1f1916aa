import enum
from pathlib import Path
import time
from functools import cache

from sqlalchemy import <PERSON>um<PERSON>, En<PERSON>, <PERSON><PERSON>ey, Integer, String, UniqueConstraint, create_engine, or_, select, update, Float
from sqlalchemy.dialects.sqlite import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Mapped, declarative_base, mapped_column, relationship, sessionmaker
from sqlalchemy.orm.attributes import flag_modified

from teslatoolbox.utils.getenv import env
from teslatoolbox.utils.logger import logger

Base = declarative_base()


class CustomerConfirmation(enum.Enum):
    UNKNOWN = 'unknown'
    CONFIRMED = 'confirmed'
    VIN_WRONG = 'vin_wrong'
    MAIL_WRONG = 'mail_wrong'


class Customer(Base):
    __tablename__ = 'customer'
    id = mapped_column(Integer, primary_key=True)
    vin = mapped_column(String, nullable=False)
    vehicle_id = mapped_column(Integer)
    tesla_mail = mapped_column(String, nullable=False)
    birthday = mapped_column(Integer)
    color = mapped_column(String)
    model = mapped_column(String)
    confirmation = mapped_column(Enum(CustomerConfirmation), default=CustomerConfirmation.UNKNOWN, nullable=False)
    wrong_details_mail_sent = mapped_column(Integer, default=0, nullable=False)
    diagnoses: Mapped[list['Diagnosis']] = relationship('Diagnosis', back_populates='customer', lazy='selectin')


class DiagnosisType(enum.Enum):
    FREE_DIAGNOSIS_LIGHT_SHOW = 'free_diagnosis_light_show'
    DEGRADATION = 'degradation'


class DiagnosisState(enum.Enum):
    INIT = 'init'

    REQUESTING_AUTHORIZATION = 'requesting_authorization'

    AUTHORIZATION_PENDING = 'authorization_pending'
    # AUTHORIZATION_APPROVED = 'authorization_approved'

    GETTING_VEHICLE_ID = 'getting_vehicle_id'
    # GOT_VEHICLE_ID = 'got_vehicle_id'

    WAKING_VEHICLE = 'waking_vehicle'
    # VEHICLE_AWAKE = 'vehicle_awake'

    REQUESTING_LOGS = 'requesting_logs'
    # LOGS_REQUESTED = 'logs_requested'

    WAITING_FOR_LOGS = 'waiting_for_logs'
    # LOGS_ADEQUATE = 'logs_adequate'

    DIAGNOSING = 'diagnosing'
    # DIAGNOSED = 'diagnosed'

    SENDING_REPORT = 'sending_report'
    # REPORT_SENT = 'report_sent'

    SENDING_FOLLOWUP_REPORT = 'sending_followup_report'

    DONE = 'done'
    FAILED = 'failed'


class Diagnosis(Base):
    __tablename__ = 'diagnosis'
    id = mapped_column(Integer, primary_key=True)
    customer_id = mapped_column(Integer, ForeignKey('customer.id'))
    customer: Mapped['Customer'] = relationship('Customer', back_populates='diagnoses', lazy='joined')  # https://stackoverflow.com/a/77570876
    diagnosis_type = mapped_column(Enum(DiagnosisType), nullable=False)
    requested_at = mapped_column(Integer, nullable=False)
    updated_at = mapped_column(Integer)
    state = mapped_column(Enum(DiagnosisState, omit_aliases=False), default=DiagnosisState.INIT, nullable=False)
    current_task_attempts = mapped_column(Integer, default=0, nullable=False)
    retry_after = mapped_column(Integer, default=0, nullable=False)
    logs_available_fraction = mapped_column(Float)
    vitals = mapped_column(JSON)
    report = mapped_column(JSON)
    error_message = mapped_column(String)


class DataStore(Base):
    __tablename__ = 'datastore'
    key = mapped_column(String, primary_key=True)
    value = mapped_column(String)
    updated_at = mapped_column(Integer)
    __table_args__ = (UniqueConstraint('key', sqlite_on_conflict='REPLACE'),)


class OauthProvider(enum.Enum):
    MICROSOFT = 'microsoft'
    TEAMLEADER = 'teamleader'


class OAuthToken(Base):
    __tablename__ = 'oauth_token'
    id = mapped_column(Integer, primary_key=True)
    user_mail = mapped_column(String, nullable=False)
    provider = mapped_column(Enum(OauthProvider), nullable=False)
    access_token = mapped_column(String, nullable=False)
    refresh_token = mapped_column(String, nullable=False)
    expires_at = mapped_column(Integer, nullable=False)
    updated_at = mapped_column(Integer)
    raw_data = mapped_column(JSON)
    user_data = mapped_column(JSON)
    __table_args__ = (UniqueConstraint('provider', 'user_mail', sqlite_on_conflict='REPLACE'),)


def init_models():
    from sqlalchemy import text

    with get_engine().begin() as conn:
        # response = await asyncio.to_thread(input, 'Type "drop" to drop all tables: ')
        # if response.lower() == 'drop':
        #     Base.metadata.drop_all(conn)
        Base.metadata.create_all(conn)
        conn.execute(text('PRAGMA journal_mode=WAL;'))


@cache
def get_engine():
    Path('database').mkdir(parents=True, exist_ok=True)
    if env == 'production':
        logger.info('Connecting to production database.')
        return create_engine('sqlite:///database/database.db')
    else:
        logger.info('Connecting to development database.')
        return create_engine('sqlite:///database/dev.db')


def get_db_session():
    engine = get_engine()
    Session = sessionmaker(bind=engine)
    return Session()


def may_retry():
    return or_(Diagnosis.retry_after <= int(time.time()), Diagnosis.retry_after == 0)


def main():
    init_models()

    with get_db_session() as db:
        # customer = Customer(vin='5YJSA3H13EFP31132', tesla_mail='<EMAIL>')
        # db.add(customer)
        # diagnosis = Diagnosis(customer=customer, requested_at=time.time(), state=DiagnosisState.AUTHORIZATION_PENDING)
        # db.add(diagnosis)
        # db.commit()

        # # display customer with highest ID
        # with get_db_session() as db:
        #     # Print last customer
        #     customer = db.scalar(select(Customer).order_by(Customer.id.desc()).limit(1))
        #     assert customer is not None
        #     print(customer.vin, customer.tesla_mail)

        # # Cancel a diagnosis
        diagnosis = db.scalar(select(Diagnosis).where(Diagnosis.id == 3))
        assert diagnosis is not None
        # print(diagnosis.customer.tesla_mail, diagnosis.state)
        # diagnosis.state = DiagnosisState.FAILED
        # diagnosis.error_message = 'Stopped by user.'
        # db.commit()

        # # # reset state
        # diagnoses = db.execute(
        #     update(Diagnosis)
        #     .where(or_(Diagnosis.state == DiagnosisState.SENDING_REPORT, Diagnosis.state == DiagnosisState.DIAGNOSING))
        #     .values(state=DiagnosisState.DIAGNOSING, retry_after=0)
        # )
        # print(diagnoses.rowcount)
        # db.commit()

        # # reset all delta v values higher than 0.009
        # diagnoses = db.scalars(select(Diagnosis))
        # for diagnosis in diagnoses:
        #     if diagnosis.report is not None and 'deltaV' in diagnosis.report and diagnosis.report['deltaV'] is not None:
        #         if diagnosis.report['deltaV']['value'] > 0.009:
        #             diagnosis.report['deltaV'] = None
        #         else:
        #             diagnosis.report['deltaV']['value'] = round(diagnosis.report['deltaV']['value'], 3)
        #         flag_modified(diagnosis, 'report')
        #     if diagnosis.report is not None and 'amountFastCharging' in diagnosis.report and diagnosis.report['amountFastCharging'] is not None:
        #         diagnosis.report['amountFastCharging']['value'] = round(diagnosis.report['amountFastCharging']['value'], 4)
        #         flag_modified(diagnosis, 'report')
        # db.commit()

        # #  edit a diagnosis
        # diagnosis = db.scalar(select(Diagnosis).where(Diagnosis.id == 28))
        # assert diagnosis is not None
        # # diagnosis.customer.vin = diagnosis.customer.vin.upper()
        # # diagnosis.customer.tesla_mail = '<EMAIL>'
        # # diagnosis.state = DiagnosisState.INIT
        # # diagnosis.error_message = None
        # diagnosis.report['isolationGrade']['value'] = 3670
        # flag_modified(diagnosis, 'report')
        # db.commit()

        # # # reset all retry_after to 0 where diagnosis state is requesting logs
        # db.execute(update(Diagnosis).where(Diagnosis.state == DiagnosisState.REQUESTING_LOGS).values(retry_after=0))
        # db.commit()

        # change something in db
        # diagnoses = db.scalars(select(Diagnosis))
        # for diagnosis in diagnoses:
        #     if diagnosis.vitals is None:
        #         continue
        #     if diagnosis.vitals['model'] == 'Lychee':
        #         diagnosis.vitals['model'] = 'S'
        #         flag_modified(diagnosis, 'vitals')
        #     elif diagnosis.vitals['model'] == 'Highland':
        #         diagnosis.vitals['model'] = '3'
        #         flag_modified(diagnosis, 'vitals')
        #     elif diagnosis.vitals['model'] == 'Tamarind':
        #         diagnosis.vitals['model'] = 'X'
        #         flag_modified(diagnosis, 'vitals')
        #     elif diagnosis.vitals['model'] == 'Juniper':
        #         diagnosis.vitals['model'] = 'Y'
        #         flag_modified(diagnosis, 'vitals')
        # db.commit()

        # # reset state
        # diagnoses = db.execute(update(Diagnosis).where(or_(Diagnosis.state == DiagnosisState.DONE)).values(state=DiagnosisState.SENDING_FOLLOWUP_REPORT, retry_after=0))
        # print(diagnoses.rowcount)
        # db.commit()

        # import calendar

        # diagnoses = db.scalars(select(Diagnosis))
        # for diagnosis in diagnoses:
        #     if diagnosis.vitals is not None:
        #         diagnosis.customer.confirmation = CustomerConfirmation.CONFIRMED
        #         flag_modified(diagnosis.customer, 'confirmation')
        #         diagnosis.customer.birthday = calendar.timegm(tuple(diagnosis.vitals['birthday']))
        #         diagnosis.customer.color = diagnosis.vitals['color']
        #         diagnosis.customer.model = diagnosis.vitals['model']
        #         flag_modified(diagnosis.customer, 'birthday')
        #         flag_modified(diagnosis.customer, 'color')
        #         flag_modified(diagnosis.customer, 'model')
        #     if diagnosis.error_message == 'Authorization request failed: Specified email does not own vin':
        #         diagnosis.customer.confirmation = CustomerConfirmation.MAIL_WRONG
        #         flag_modified(diagnosis.customer, 'confirmation')
        #     if diagnosis.error_message is not None and diagnosis.error_message[:12] == 'Failed after' or diagnosis.error_message == 'Authorization request failed: VIN is wrong.':
        #         diagnosis.customer.confirmation = CustomerConfirmation.VIN_WRONG
        #         flag_modified(diagnosis.customer, 'confirmation')
        #     if diagnosis.state == DiagnosisState.AUTHORIZATION_PENDING or diagnosis.error_message == 'Authorization rejected.':
        #         diagnosis.customer.confirmation = CustomerConfirmation.CONFIRMED
        #         flag_modified(diagnosis.customer, 'confirmation')
        # db.commit()

        pass


if __name__ == '__main__':
    main()
