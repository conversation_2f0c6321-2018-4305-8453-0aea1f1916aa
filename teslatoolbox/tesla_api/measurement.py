import calendar
import time
from typing import Optional
from teslatoolbox.utils.logger import logger
from teslatoolbox.utils.mymath import clip
from teslatoolbox.tesla_api.tesla_api import get_vehicle_log_availability, get_vehicle_signals
from teslatoolbox.tesla_api.tesla_types import BMS_ContactorStateMap, CleanVehicleSummary, FloatMeasurement, IntMeasurement, UnixTimestampMS, VehicleSummary


def cleanup_vehicle_summary(vehicleSummary: VehicleSummary) -> CleanVehicleSummary:
    keys = {
        'vin': ['vin'],
        'odometer': ['odo'],
        'birthday': ['bday'],
        'color': ['cfg_exteriorcolor'],
        'model': ['cfg_car'],
        'time': ['time'],  # "Wed Feb 26 09:38:59 2025"
        'min_brick_volt': ['min_brick_volt'],
        'max_brick_volt': ['max_brick_volt'],
        'BMS_contactorState': ['bms_contactor_state', 'BMS_contactorState'],
        'BMS_cacMin': ['BMS_cacMin', 'bms_cac_min'],
        'BMS_cacMax': ['BMS_cacMax', 'bms_cac_max'],
        'BMS_cacAvg': ['BMS_cacAvg', 'bms_cac_avg'],
        'BMS_isolationResistance': ['BMS_isolationResistance', 'bms_iso'],
    }
    result = {}
    for key, possibleKeys in keys.items():
        for possibleKey in possibleKeys:
            if possibleKey in vehicleSummary:
                print(f'{key}: {possibleKey} = {vehicleSummary[possibleKey]}')
                if vehicleSummary[possibleKey] == '--':
                    result[key] = None
                else:
                    result[key] = vehicleSummary[possibleKey]
                break
        if key not in result:
            result[key] = None
    if result['odometer'] is not None:
        result['odometer'] = round(result['odometer'] * 1.60934, 3)
    if result['birthday'] is not None:
        result['birthday'] = time.strptime(result['birthday'], '%a %b %d %H:%M:%S %Y')
    if result['model'] is not None:
        if result['model'] == 'Lychee':
            result['model'] = 'S'
        elif result['model'] == 'Highland':
            result['model'] = '3'
        elif result['model'] == 'Tamarind':
            result['model'] = 'X'
        elif result['model'] == 'Juniper':
            result['model'] = 'Y'
    if result['time'] is not None:
        result['time'] = calendar.timegm(time.strptime(result['time'], '%a %b %d %H:%M:%S %Y'))
    if result['min_brick_volt'] is not None:
        result['min_brick_volt'] = round(float(result['min_brick_volt']), 3)
    if result['max_brick_volt'] is not None:
        result['max_brick_volt'] = round(float(result['max_brick_volt']), 3)
    if result['BMS_contactorState'] is not None:
        if result['BMS_contactorState'] in BMS_ContactorStateMap.keys():
            result['BMS_contactorState'] = BMS_ContactorStateMap[result['BMS_contactorState']]
    if result['BMS_cacMin'] is not None:
        result['BMS_cacMin'] = round(float(result['BMS_cacMin']), 3)
    if result['BMS_cacMax'] is not None:
        result['BMS_cacMax'] = round(float(result['BMS_cacMax']), 3)
    if result['BMS_cacAvg'] is not None:
        result['BMS_cacAvg'] = round(float(result['BMS_cacAvg']), 3)
    if result['BMS_isolationResistance'] is not None:
        result['BMS_isolationResistance'] = int(result['BMS_isolationResistance'])
    return result  # type: ignore


def get_vehicle_log_availability_fraction(vin: str) -> float:
    """
    Return the fraction of the time that has logs available for the last 7 days excluding the last day.

    1 = all logs are available
    0 = no logs are available
    """
    end = UnixTimestampMS((time.time() - 1 * 24 * 60 * 60) * 1000)  # 1 day ago; last day is always kind of missing
    start = UnixTimestampMS(end - 6 * 24 * 60 * 60 * 1000)  # 1 + 6 = 7 days

    intervals = get_vehicle_log_availability(vin, start, end)
    totalMS = end - start
    availableMS = 0
    for interval1 in intervals:
        for interval2 in intervals:
            if interval1 == interval2:
                continue
            if interval2['start'] < interval1['end'] and interval2['end'] > interval2['start']:
                # Overlapping intervals
                interval1['start'] = min(interval1['start'], interval2['start'])
                interval1['end'] = max(interval1['end'], interval2['end'])
                interval2['start'] = UnixTimestampMS(0)
                interval2['end'] = UnixTimestampMS(0)
    for interval in intervals:
        availableMS += clip(start, interval['end'], end) - clip(start, interval['start'], end)
    return availableMS / totalMS


def can_last_internal_isolation_resistance(vin: str) -> Optional[IntMeasurement]:
    signals = get_vehicle_signals(vin, signal_names=['BMS_isolationResistanceInternal', 'PT_BMS_isolationResistanceInternal'])
    if not (('BMS_isolationResistanceInternal' in signals) or ('PT_BMS_isolationResistanceInternal' in signals)):
        return None
    iso_r_signal = signals['BMS_isolationResistanceInternal'] if 'BMS_isolationResistanceInternal' in signals else signals['PT_BMS_isolationResistanceInternal']
    for is_sna, value, timestamp in zip(iso_r_signal['is_sna'][::-1], iso_r_signal['sig_value'][::-1], iso_r_signal['timestamps'][::-1]):
        if is_sna:
            continue
        return IntMeasurement(value=int(value), signal=iso_r_signal['metadata']['sig_name'], timestamp=timestamp)


def can_last_internal_isolation_resistance_by_total(vin: str) -> Optional[IntMeasurement]:
    """
    Get the last isolation resistance for a vehicle of the last 7 days.
    Do this by looking at the second last time the contactors were closed, and taking the max value.
    """
    signals = get_vehicle_signals(vin, signal_names=['BMS_contactorState', 'BMS_isolationResistance', 'PT_BMS_contactorState', 'PT_BMS_isolationResistance'])
    if not (('BMS_contactorState' in signals and 'BMS_isolationResistance' in signals) or ('PT_BMS_contactorState' in signals and 'PT_BMS_isolationResistance' in signals)):
        return None
    contactor_state_signal = signals['BMS_contactorState'] if 'BMS_contactorState' in signals else signals['PT_BMS_contactorState']
    iso_r_signal = signals['BMS_isolationResistance'] if 'BMS_isolationResistance' in signals else signals['PT_BMS_isolationResistance']
    last_contactor_state_open = None
    # Walk backwards through the contactor state signal to find the second last time the contactors were open
    for is_sna, sig_text, timestamp in zip(contactor_state_signal['is_sna'][::-1], contactor_state_signal['sig_text'][::-1], contactor_state_signal['timestamps'][::-1]):
        if is_sna:
            continue
        if sig_text.find('OPEN') != -1:
            if last_contactor_state_open is None:
                last_contactor_state_open = timestamp  # First time the contactors were open
            else:
                last_contactor_state_open = timestamp  # Second time the contactors were open
                break
    if last_contactor_state_open is None:
        return None

    # Add some margin
    last_contactor_state_open -= 5 * 60 * 1000

    # Walk backwards until the last time the contactors were open (or take the last 10 values), and take the max value of the isolation resistance
    max_value = -1
    timestamp_of_value = None
    find_n_values = 10
    for is_sna, value, timestamp in zip(iso_r_signal['is_sna'][::-1], iso_r_signal['sig_value'][::-1], iso_r_signal['timestamps'][::-1]):
        if is_sna:
            continue
        if timestamp < last_contactor_state_open and find_n_values <= 0:
            break
        find_n_values -= 1
        if value > max_value:
            max_value = value
            timestamp_of_value = timestamp
    if max_value == -1 or timestamp_of_value is None:
        return None
    return IntMeasurement(value=int(max_value), signal=iso_r_signal['metadata']['sig_name'], timestamp=timestamp_of_value)


def can_max_brick_voltage_discrepancy(vin: str) -> Optional[FloatMeasurement]:
    """Return the max brick voltage discrepancy in the last 7 days.
    Returns None if the data is not available.
    Only measurements during an idle car are considered. (Not charging or driving.)"""
    signals = get_vehicle_signals(vin, signal_names=['BMS_brickVoltageMin', 'BMS_brickVoltageMax', 'PT_BMS_brickVoltageMin', 'PT_BMS_brickVoltageMax', 'BMS_state', 'PT_BMS_state'])
    if not (
        (('BMS_brickVoltageMin' in signals and 'BMS_brickVoltageMax' in signals) or ('PT_BMS_brickVoltageMin' in signals and 'PT_BMS_brickVoltageMax' in signals))
        and ('BMS_state' in signals or 'PT_BMS_state' in signals)
    ):
        return None
    vmin_signal = signals['BMS_brickVoltageMin'] if 'BMS_brickVoltageMin' in signals else signals['PT_BMS_brickVoltageMin']
    vmax_signal = signals['BMS_brickVoltageMax'] if 'BMS_brickVoltageMax' in signals else signals['PT_BMS_brickVoltageMax']
    state_signal = signals['BMS_state'] if 'BMS_state' in signals else signals['PT_BMS_state']
    if vmin_signal['timestamps'] != vmax_signal['timestamps']:
        return None
    max_discrepancy = -1
    timestamp_of_discrepancy = None
    standby = False
    state_signal = list(zip(state_signal['sig_text'], state_signal['is_sna'], state_signal['timestamps']))
    state_signal_index = -1
    for vmin, vmax, is_rna, timestamp in zip(vmin_signal['sig_value'], vmax_signal['sig_value'], vmin_signal['is_sna'], vmin_signal['timestamps']):
        if is_rna:  # Just to be sure: normally this data should not contain any RNA values
            continue
        while len(state_signal) > state_signal_index + 1 and state_signal[state_signal_index + 1][2] <= timestamp:
            state_signal_index += 1
            if state_signal[state_signal_index][1]:
                continue
            if state_signal[state_signal_index][0].find('STANDBY') != -1:
                standby = True
            else:
                standby = False
        if not standby:
            continue
        if vmax - vmin > max_discrepancy:
            max_discrepancy = vmax - vmin
            timestamp_of_discrepancy = timestamp
    if max_discrepancy == -1 or timestamp_of_discrepancy is None:
        return None
    return FloatMeasurement(
        value=round(max_discrepancy, 3), signal=f'{vmax_signal["metadata"]["sig_name"]} - {vmin_signal["metadata"]["sig_name"]}', timestamp=timestamp_of_discrepancy
    )


def can_soft_max_brick_voltage_discrepancy_progressive_margin(vin: str) -> Optional[FloatMeasurement]:
    for look_margin in range(0, 10):
        if look_margin > 0:
            logger.trace(f'[{vin}] Delta V trying look_margin={look_margin}')
        result = can_soft_max_brick_voltage_discrepancy(vin, look_margin)
        if result is not None:
            return result
    return None


def can_soft_max_brick_voltage_discrepancy(vin: str, look_margin=0) -> Optional[FloatMeasurement]:
    """Returns to 70th percentile of the brick voltage discrepancy in the last 7 days.
    Returns None if the data is not available.
    Only measurements during an idle car are considered. (Not charging or driving.)
    If look_margin > 0, this is the number of seconds before and after each standby state that is considered."""
    signals = get_vehicle_signals(vin, signal_names=['BMS_brickVoltageMin', 'BMS_brickVoltageMax', 'PT_BMS_brickVoltageMin', 'PT_BMS_brickVoltageMax', 'BMS_state', 'PT_BMS_state'])
    if not (
        (('BMS_brickVoltageMin' in signals and 'BMS_brickVoltageMax' in signals) or ('PT_BMS_brickVoltageMin' in signals and 'PT_BMS_brickVoltageMax' in signals))
        and ('BMS_state' in signals or 'PT_BMS_state' in signals)
    ):
        return None
    vmin_signal = signals['BMS_brickVoltageMin'] if 'BMS_brickVoltageMin' in signals else signals['PT_BMS_brickVoltageMin']
    vmax_signal = signals['BMS_brickVoltageMax'] if 'BMS_brickVoltageMax' in signals else signals['PT_BMS_brickVoltageMax']
    state_signal = signals['BMS_state'] if 'BMS_state' in signals else signals['PT_BMS_state']
    if vmin_signal['timestamps'] != vmax_signal['timestamps']:
        # filter out values that are not equal
        logger.trace(f'[{vin}] Delta V: Filtering out values that are not equal')
        matching_timestamps = list(set(vmin_signal['timestamps']) & set(vmax_signal['timestamps']))
        matching_timestamps.sort()
        vmin_signal = {
            'sig_value': [vmin_signal['sig_value'][vmin_signal['timestamps'].index(timestamp)] for timestamp in matching_timestamps],
            'is_sna': [vmin_signal['is_sna'][vmin_signal['timestamps'].index(timestamp)] for timestamp in matching_timestamps],
            'timestamps': matching_timestamps,
            'metadata': vmin_signal['metadata'],
        }
        vmax_signal = {
            'sig_value': [vmax_signal['sig_value'][vmax_signal['timestamps'].index(timestamp)] for timestamp in matching_timestamps],
            'is_sna': [vmax_signal['is_sna'][vmax_signal['timestamps'].index(timestamp)] for timestamp in matching_timestamps],
            'timestamps': matching_timestamps,
            'metadata': vmax_signal['metadata'],
        }
        logger.trace(f'[{vin}] Delta V: {len(vmin_signal["timestamps"])} matching timestamps found')
    if len(vmin_signal['timestamps']) == 0:
        logger.warning(f'[{vin}] Delta V: No matching timestamps found for brick voltage signals')
        return None
    discrepancy = []
    standby = False
    state_signal = list(zip(state_signal['sig_text'], state_signal['is_sna'], state_signal['timestamps']))
    state_signal_index = -1
    for vmin, vmax, is_rna, timestamp in zip(vmin_signal['sig_value'], vmax_signal['sig_value'], vmin_signal['is_sna'], vmin_signal['timestamps']):
        if is_rna:  # Just to be sure: normally this data should not contain any RNA values
            continue
        # while len(state_signal) > state_signal_index + 1 and (
        #     state_signal[state_signal_index + 1][2] + look_margin * 1000 <= timestamp
        #     or (look_margin > 0 and state_signal[state_signal_index + 1][2] - look_margin * 1000 <= timestamp and state_signal[state_signal_index + 1][0].find('STANDBY') != -1)
        # ):
        #     state_signal_index += 1
        #     if state_signal[state_signal_index][1]:
        #         continue
        #     if state_signal[state_signal_index][0].find('STANDBY') != -1:
        #         standby = True
        #     else:
        #         standby = False
        # if not standby:
        #     continue
        discrepancy.append(vmax - vmin)
    if len(discrepancy) == 0:
        return None
    discrepancy.sort()
    percentile = discrepancy[int(len(discrepancy) * 0.50)]
    return FloatMeasurement(value=round(percentile, 3), signal=f'median({vmax_signal["metadata"]["sig_name"]}-{vmin_signal["metadata"]["sig_name"]})', timestamp=None)


def can_last_dc_charger_kwh_total(vin: str) -> Optional[FloatMeasurement]:
    signals = get_vehicle_signals(vin, signal_names=['BMS_dcChargerKwhTotal', 'PT_BMS_dcChargerKwhTotal'])
    if not (('BMS_dcChargerKwhTotal' in signals) or ('PT_BMS_dcChargerKwhTotal' in signals)):
        return None
    dc_charger_kwh_signal = signals['BMS_dcChargerKwhTotal'] if 'BMS_dcChargerKwhTotal' in signals else signals['PT_BMS_dcChargerKwhTotal']
    for is_sna, value, timestamp in zip(dc_charger_kwh_signal['is_sna'][::-1], dc_charger_kwh_signal['sig_value'][::-1], dc_charger_kwh_signal['timestamps'][::-1]):
        if is_sna:
            continue
        return FloatMeasurement(value=value, signal=dc_charger_kwh_signal['metadata']['sig_name'], timestamp=timestamp)


def can_last_ac_charger_kwh_total(vin: str) -> Optional[FloatMeasurement]:
    signals = get_vehicle_signals(vin, signal_names=['BMS_acChargerKwhTotal', 'PT_BMS_acChargerKwhTotal'])
    if not (('BMS_acChargerKwhTotal' in signals) or ('PT_BMS_acChargerKwhTotal' in signals)):
        return None
    ac_charger_kwh_signal = signals['BMS_acChargerKwhTotal'] if 'BMS_acChargerKwhTotal' in signals else signals['PT_BMS_acChargerKwhTotal']
    for is_sna, value, timestamp in zip(ac_charger_kwh_signal['is_sna'][::-1], ac_charger_kwh_signal['sig_value'][::-1], ac_charger_kwh_signal['timestamps'][::-1]):
        if is_sna:
            continue
        return FloatMeasurement(value=value, signal=ac_charger_kwh_signal['metadata']['sig_name'], timestamp=timestamp)


if __name__ == '__main__':
    # print(can_soft_max_brick_voltage_discrepancy('LRW3E7FA9MC222397', look_margin=5))
    # print(can_soft_max_brick_voltage_discrepancy_progressive_margin('5YJXCCE27JF126425'))
    # print(can_soft_max_brick_voltage_discrepancy_progressive_margin('LRW3E7FR4NC559759'))
    print(can_soft_max_brick_voltage_discrepancy('XP7YGCES5SB537489'))
    # print(can_last_internal_isolation_resistance('7SAXCCE60PF367810'))
    # print(can_last_internal_isolation_resistance_by_total('LRW3E7EK1MC345833'))
    # print(can_last_internal_isolation_resistance_by_total('XP7YGCEK7RB429255'))
    # print(can_last_internal_isolation_resistance('5YJ3E7EB0KF383637'))
    # print(can_last_internal_isolation_resistance_by_total('5YJ3E7EB0KF383637'))
    # print(can_last_internal_isolation_resistance('5YJXCCE27JF104375'))
    # print(can_last_internal_isolation_resistance_by_total('5YJXCCE27JF104375'))
    # print(can_is_brick_voltage_discrepancy('5YJXCCE27JF104375'))
    # print(can_is_brick_voltage_discrepancy('5YJ3E7EB0KF383637'))
    # print(can_is_brick_voltage_discrepancy('XP7YGCEK7RB429255'))
