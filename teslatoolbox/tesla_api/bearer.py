import time
from typing import Literal
from sqlalchemy import insert, select
from teslatoolbox.db import get_db_session, DataStore
from teslatoolbox.tesla_api.tesla_types import TokenExpiredError

bearer_cache = None


def has_bearer() -> str | Literal[False]:
    try:
        return get_bearer()
    except TokenExpiredError:
        return False


def get_bearer() -> str:
    global bearer_cache
    if bearer_cache is not None:
        return bearer_cache
    with get_db_session() as db:
        token = db.scalar(select(DataStore.value).where(DataStore.key == 'tbx_token'))
        if token is None:
            raise TokenExpiredError('No token found in the database.')
        bearer_cache = token
        return token


def update_bearer(bearer):
    global bearer_cache
    bearer_cache = bearer
    with get_db_session() as db:
        db.execute(insert(DataStore).values(key='tbx_token', value=bearer, updated_at=int(time.time())))
        db.commit()


def remove_bearer():
    global bearer_cache
    bearer_cache = None
    with get_db_session() as db:
        token = db.scalar(select(DataStore).where(DataStore.key == 'tbx_token'))
        if token is not None:
            db.delete(token)
            db.commit()


if __name__ == '__main__':
    print(
        update_bearer(
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.icLoIXcleweaDCMuYhg5NzuLl8sJZl8ffVp296nAJw0'
        )
        # remove_bearer()
    )
