import time
from typing import Any, Dict, List, Optional

import requests
from teslatoolbox.tesla_api.bearer import get_bearer
from teslatoolbox.tesla_api.captcha import get_captcha_token_for_request_authorization
from teslatoolbox.utils.logger import logger
from teslatoolbox.tesla_api.tesla_types import (
    VIN,
    Al<PERSON>,
    Auth,
    TokenExpiredError,
    LimitReachedError,
    LogAvailability,
    LogInterval,
    NoAuthorizationError,
    RequestErrorJSON,
    Signal,
    UnixTimestampMS,
    VehicleSummary,
    VehicleSummaryResponse,
)


def get_common_headers(referrer: str = None) -> Dict[str, str]:
    """
    Generate common headers used in Tesla API requests.

    Args:
        referrer: Optional referrer URL to include in the headers

    Returns:
        Dictionary of common headers
    """
    headers = {
        'accept': '*/*',
        'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8,nl;q=0.7',
        'authorization': f'Bearer {get_bearer()}',
        'cache-control': 'no-cache',
        'pragma': 'no-cache',
        'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
    }

    if referrer:
        headers['referrer'] = referrer
        headers['referrerPolicy'] = 'strict-origin-when-cross-origin'

    return headers


def get_auth_list(from_time=None) -> Optional[dict[VIN, Auth]]:
    """
    Get a list of all auth requests sent to vehicle owners.

    The from_time parameter is an ISO 8601 timestamp with milliseconds. If not provided, it defaults to two weeks ago.
        Example: "2023-01-06T23:16:04.182Z"
    """

    now = time.time()

    if from_time is None:
        # ISO 8601 timestamp of two weeks ago
        from_time = time.gmtime(now - 14 * 24 * 60 * 60)

    from_time_str = time.strftime('%Y-%m-%dT%H:%M:%S', from_time)
    milliseconds = int((now - int(now)) * 1000)  # Maybe we stay under the radar just a bit longer by filling this in
    from_time_str = f'{from_time_str}.{milliseconds:03d}Z'

    headers = get_common_headers(referrer='https://toolbox.tesla.com/tools/can_explorer')
    request = requests.get(
        f'https://toolbox.tesla.com/api/v2/auth/vin_access/history?from_time={from_time_str}',
        headers=headers,
    )

    try:
        response: List[Auth] | RequestErrorJSON = request.json()
    except requests.exceptions.JSONDecodeError:
        logger.error(f'Error decoding auth list JSON: {request.text}')
        return None

    if not isinstance(response, List):
        if response['error'] == 'Token Expired' or response['error'] == 'Invalid Token':
            raise TokenExpiredError(response)
        if response['type'] == '500 Internal Server Error':
            logger.error(str(response))
            return None
        else:
            raise Exception(str(response))

    # return {auth['vin']: auth for auth in response}
    order = ['Approved', 'Pending', 'Expired', 'Rejected']
    resp_obj: dict[VIN, Auth] = {}
    for auth in response:
        if auth['vin'] in resp_obj:
            # duplicate: save the one with the best status
            for status in order:
                if auth['status'] == status:
                    resp_obj[auth['vin']] = auth
                    break
                elif resp_obj[auth['vin']]['status'] == status:
                    break
        else:
            resp_obj[auth['vin']] = auth

    return resp_obj


def request_auth(vin: str, tesla_mail: str):
    captcha_token = get_captcha_token_for_request_authorization()
    if captcha_token is None:
        logger.error('Error getting captcha token for request authorization.')
        return 'captcha_error'

    headers = get_common_headers(referrer='https://toolbox.tesla.com/ownerAuthorizationRequests')
    request = requests.post(
        f'https://toolbox.tesla.com/api/v2/auth/vin_access?vin={vin}&reason=Remote%20diagnostics&customer_email={tesla_mail}&locale=en_US&captchaToken={captcha_token}',
        headers=headers,
    )

    try:
        response: Any | RequestErrorJSON = request.json()
    except requests.exceptions.JSONDecodeError:
        logger.error(f'Error decoding auth request JSON: {request.text}')
        raise Exception(str(request.text))

    if 'error' in response:
        if response['error'] == 'Failed captcha':
            return 'captcha_error'
        elif response['error'] == 'Token Expired' or response['error'] == 'Invalid Token':
            raise TokenExpiredError(response)
        elif (
            response['error'] == 'Failed to check email vin ownership'
            or response['error'] == 'Invalid vin'
        ):
            return 'vin_wrong'
        elif response['error'].find('does not own vin') != -1:
            return 'mail_wrong'
        else:
            logger.error(f'Error requesting authorization for {vin}: {response}')
            raise Exception(str(response))
    # response: {'vin_access': {'status': 'Pending', 'customer_email': '<EMAIL>', 'created_at': '2025-02-26T15:55:33.111132905Z',
    #            'expires': '2025-03-05T15:55:31Z', 'vin': 'LRW3E7FS2PC674057', 'reason': 'Remote diagnostics'}, 'message_sent': True}
    return 'success'


def get_vehicle_id(vin: str) -> Optional[str]:
    headers = get_common_headers(referrer='https://toolbox.tesla.com/tools/garage')
    request = requests.get(
        f'https://toolbox.tesla.com/api/garage-external/vehicles/{vin}',
        headers=headers,
    )

    response = request.text

    if len(response) == 0:
        return None

    # find the id between "vid=" and "\u0026"
    start = response.find('vid=') + 4
    end = response.find('\\u0026', start)

    if start == -1 or end == -1:
        return None

    return response[start:end]


def poke_vehicle(vin: str):
    headers = get_common_headers(referrer='https://toolbox.tesla.com/tools/garage')
    # Add specific headers needed for this request
    headers.update(
        {
            'accept': 'application/json, text/plain, */*',
            'connection': 'keep-alive',
            'content-length': '27',
            'content-type': 'application/json;charset=UTF-8',
            'dnt': '1',
            'host': 'toolbox.tesla.com',
            'origin': 'https://toolbox.tesla.com',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        }
    )

    request = requests.post(
        'https://toolbox.tesla.com/api/autodiag/wake_up',
        headers=headers,
        json={'vin': vin},
    )

    if request.status_code == 504:
        # Timeout
        return False
    elif request.status_code == 403:
        raise NoAuthorizationError('No authorization to wake up vehicle.')

    try:
        data = request.json()
    except requests.exceptions.JSONDecodeError:
        logger.error(f'Error decoding wake up JSON: {request.text}')
        return False

    if 'error' in data:
        logger.error(f'Error waking up vehicle: {data["error"]}')
        return False

    if data['response'] == 'ok':
        return True
    else:
        logger.error(f'Error waking up vehicle: {data}')
        return False


def get_vehicle_summary(car_id: int | str) -> Optional[VehicleSummary]:
    headers = get_common_headers()
    headers['referrerPolicy'] = 'no-referrer'  # Override the default referrer policy
    request = requests.get(
        f'https://toolbox.tesla.com/api/garage-external/api/1/vehicles/{car_id}/vehicle_summary?device_type=vehicle',
        headers=headers,
    )

    try:
        response: VehicleSummaryResponse | RequestErrorJSON = request.json()
    except requests.exceptions.JSONDecodeError:
        logger.warning(f'Error decoding vehicle summary JSON: {request.text}')
        return None

    if 'error' in response:
        if response['error'] == 'Token Expired':
            raise TokenExpiredError(response)
        elif response['error'] == 'you may not access this feature (403)':
            raise NoAuthorizationError(response)
        else:
            raise Exception(str(response))

    data = response['response']
    data['_12VBatteryCurrent'] = data['12VBatteryCurrent']  # type: ignore
    data['_12VBatteryTemp'] = data['12VBatteryTemp'] if '12VBatteryTemp' in data else None  # type: ignore
    data['_12VBatteryVoltage'] = data['12VBatteryVoltage']  # type: ignore

    return data


def request_logs(vin: str, start: UnixTimestampMS = None, end: UnixTimestampMS = None):
    if end is None:
        end = UnixTimestampMS(time.time() * 1000)
    if start is None:
        start = UnixTimestampMS(end - 14 * 24 * 60 * 60 * 1000)

    headers = get_common_headers(referrer=f'https://toolbox.tesla.com/tools/can_explorer?productId={vin}')
    request = requests.post(
        'https://toolbox.tesla.com/api/autodiag/request_log_pull',
        json={'product_id': vin, 'start': start, 'end': end, 'check_in_progress': True, 'file_id': None, 'file_offset': None, 'log_length': None, 'date_offset': 1},
        headers=headers,
    )

    if request.text == '401: Unauthorized':
        raise TokenExpiredError(f'Token expired or invalid when requesting logs for {vin}.')
    if request.text == '403: Forbidden':
        raise NoAuthorizationError(f'No authorization for vin {vin}.')
    if request.text.find('out of 100 allowed') > 0:
        raise LimitReachedError('Log request limit reached: {request.text}')

    try:
        response = request.json()
    except requests.exceptions.JSONDecodeError:
        raise ValueError(f'Response text when requestion logs for {vin}: "{request.text}"')

    if response['error'] != '':
        if response['error'] == 'log pull in progress':
            logger.warning(f'[{vin}] Log pull already in progress.')
            return True
        logger.error(f'Error requesting logs for {vin}: "{response["error"]}"')
        return False
    return True


def can_request(vin, url, start: UnixTimestampMS = None, end: UnixTimestampMS = None, query=''):
    if end is None:
        end = UnixTimestampMS(time.time() * 1000)
    if start is None:
        start = UnixTimestampMS(end - 14 * 24 * 60 * 60 * 1000)  # 14 days

    if len(query) > 0:
        query = f'&{query}'

    headers = get_common_headers(referrer=f'https://toolbox.tesla.com/tools/can_explorer?productId={vin}')
    request = requests.get(
        f'https://toolbox.tesla.com/api/v3/datatank/{url}?key=vin:{vin}{query}&start={start}&end={end}&expand=true',
        headers=headers,
    )

    if request.text.strip() == 'Please request owner authorization for this VIN.':
        raise NoAuthorizationError(f'No authorization for vin {vin}.')
    elif request.text.strip() == 'Start and End date should be in Unix Epoch millisecond format.':
        raise ValueError('Start and End date should be in Unix Epoch milliseconds format.')
    elif request.text.strip() == 'Invalid VIN':
        raise ValueError(f'Invalid VIN: {vin}')

    try:
        response: Any | RequestErrorJSON = request.json()
    except requests.exceptions.JSONDecodeError:
        logger.error(f'Error decoding JSON: {request.text}')
        logger.debug(f'headers: {headers}')
        logger.debug(f'url: {request.url}')
        raise TokenExpiredError(f'Response text: {request.text}')

    if isinstance(response, dict) and 'error' in response:
        if response['error'] == 'Token Expired' or response['error'] == 'Invalid Token':
            raise TokenExpiredError(response)
        else:
            raise Exception(str(response))

    response_any: Any = response

    return response_any


def get_vehicle_log_availability(vin: str, start: UnixTimestampMS = None, end: UnixTimestampMS = None) -> list[LogInterval]:
    """
    Get the availability of logs for a vehicle.

    Start and end are Unix timestamps in seconds. If not provided, start defaults to 7 days ago, and end defaults to now.
    """

    response: list[LogAvailability] = can_request(vin, 'dataLocation', start, end)

    result: list[LogInterval] = []
    for obj in response:
        for interval in obj['intervals']:
            result.append(interval)
    return result


def get_vehicle_alerts(vin, start: UnixTimestampMS = None, end: UnixTimestampMS = None, alert_name: str = None) -> List[Alert]:
    """
    Get alerts for a vehicle.

    Start and end are Unix timestamps in seconds. If not provided, start defaults to 7 days ago, and end defaults to now.

    *Beware*: When no alert_name is provided, alert data might be omitted. Use this function first without alert_name to get a list of all alerts,
    then use the alert_name to get the data for a specific alert.
    """

    if alert_name is not None:
        query = f'&filter=alert_name=%27{alert_name}%27'
    else:
        query = ''

    response: List[Alert] = can_request(vin, 'alerts', start, end, query)

    return response


def get_vehicle_signals(vin, start: UnixTimestampMS = None, end: UnixTimestampMS = None, signal_names: str | list[str] = None) -> dict[str, Signal]:
    """
    Get signals for a vehicle.

    Start and end are Unix timestamps in seconds. If not provided, start defaults to 7 days ago, and end defaults to now.
    """

    if signal_names is not None:
        if isinstance(signal_names, list):
            responses = {}
            for signal_name in signal_names:
                response: dict[str, Signal] = get_vehicle_signals(vin, start, end, signal_name)
                if signal_name in response:
                    responses[signal_name] = response[signal_name]
            return responses
        else:
            query = f'&filter=sig_name=%27{signal_names}%27'
    else:
        query = ''

    response: dict[str, Signal] = can_request(vin, 'signals', start, end, query)

    return response


def main():
    # get_auth_list()
    # request_auth('XP7YGCEJ0PB213834', '<EMAIL>')
    # request_logs('5YJ3E7EB0KF383637', start=UnixTimestampMS(1738438058181), end=UnixTimestampMS(1739891374932))
    # print(get_vehicle_signals('5YJ3E7EB0KF383637', signal_names=['BMS_brickVoltageMin', 'BMS_brickVoltageMax']))


    # print(get_vehicle_signals('5YJ3E7EB0KF383637', signal_name='BMS_brickVoltageMin'))
    # print(get_vehicle_signals('5YJ3E7EB0KF383637', signal_name='BMS_brickVoltageMax'))
    # auth_json = get_auth_list()
    # f = open('data/auth.json', 'w')
    # f.write(str(auth_json))
    # f.close()

    vehicle_id = get_vehicle_id('5YJSA7E43KF343125')
    # vehicle_id = 1612892853
    print(vehicle_id)

    # vitals = get_vehicle_summary(vehicle_id)
    # f = open('data/vitals_5YJ3E7EB0KF383637.json', 'w')
    # f.write(str(vitals))
    # f.close()

    # alerts_all = get_alerts('5YJSA3H17EFP30100')
    # f = open('data/alerts.json', 'w')
    # f.write(str(alerts_all))
    # f.close()

    # alerts_filtered = get_alerts('5YJSA3H17EFP30100', alert_name='BMS_a097_HW_BMB_Under_Voltage')
    # f = open('data/alerts_filtered.json', 'w')
    # f.write(str(alerts_filtered))
    # f.close()

if __name__ == '__main__':
    main()
