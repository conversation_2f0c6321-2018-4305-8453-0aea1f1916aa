from dataclasses import dataclass
from enum import Enum
from typing import Optional, TypedDict
from teslatoolbox.tesla_api.measurement import (
    can_last_ac_charger_kwh_total,
    can_last_dc_charger_kwh_total,
    can_last_internal_isolation_resistance,
    can_last_internal_isolation_resistance_by_total,
    can_soft_max_brick_voltage_discrepancy_progressive_margin,
)
from teslatoolbox.tesla_api.tesla_types import CleanVehicleSummary, FloatMeasurement, IntMeasurement, Measurement


class Grade(Enum):
    GOOD = 0
    MAINTENANCE_REQUIRED = 1
    SEVERE_PROBLEM = 2


class GradeWithMeasurement(TypedDict):
    grade: Grade
    measurement: Measurement


class Report(TypedDict):
    isolationGrade: Optional[IntMeasurement]
    amountFastCharging: Optional[FloatMeasurement]
    deltaV: Optional[FloatMeasurement]


def createReport(vitals: CleanVehicleSummary) -> Report:
    return Report(
        isolationGrade=getInternalIsolationResistance(vitals),
        amountFastCharging=calculateAmountFastCharging(vitals),
        deltaV=can_soft_max_brick_voltage_discrepancy_progressive_margin(vitals['vin']),
    )


def getInternalIsolationResistance(vitals: CleanVehicleSummary) -> Optional[IntMeasurement]:
    # Try if the vehicle directly reports internal isolation resistance
    internal_resistance = can_last_internal_isolation_resistance(vitals['vin'])
    if internal_resistance is not None:
        return internal_resistance

    # Try to get the value from total isolation resistance and contactor state
    internal_resistance = can_last_internal_isolation_resistance_by_total(vitals['vin'])
    if internal_resistance is not None:
        return internal_resistance

    # Back up method: Try if vitals data is enough
    return getIsolationResistanceFromVitals(vitals)


def getIsolationResistanceFromVitals(vitals: CleanVehicleSummary) -> Optional[IntMeasurement]:
    """
    Guidance values:
        Isolation resistance internal: > 3600: perfect, 3200 - 3600: maintenance required, < 3200: problem
        Isolation resistance total: >= 1000 is good, < 1000 is problem
    Contactors:
        Contactors OPEN:   Isolation resistance = internal
        Contactors CLOSED: Isolation resistance = total
    """
    if vitals['BMS_isolationResistance'] is None:
        return None
    if vitals['BMS_contactorState'] and vitals['BMS_contactorState'].find('OPEN') != -1:
        return IntMeasurement(value=vitals['BMS_isolationResistance'], signal='BMS_isolationResistance_vitals_contactorOpen', timestamp=None)
    return None


def grade_internal_isolation_resistance(internal_isolation_resistance: int) -> Grade:
    """
    Guidance values:
        Isolation resistance internal: > 3600: perfect, 3200 - 3600: maintenance required, < 3200: problem

    Contactors OPEN: Isolation resistance = internal
    """
    if internal_isolation_resistance > 3600:
        return Grade.GOOD
    elif internal_isolation_resistance >= 3200:
        return Grade.MAINTENANCE_REQUIRED
    else:
        return Grade.SEVERE_PROBLEM


def calculateAmountFastCharging(vitals: CleanVehicleSummary) -> Optional[FloatMeasurement]:
    dc_charging = can_last_dc_charger_kwh_total(vitals['vin'])  # dc = fast charging
    ac_charging = can_last_ac_charger_kwh_total(vitals['vin'])
    if dc_charging is None or ac_charging is None:
        return None
    return FloatMeasurement(
        value=round(dc_charging['value'] / (dc_charging['value'] + ac_charging['value']), 4),
        signal='percentage_fast_charging',
        timestamp=max(dc_charging['timestamp'], ac_charging['timestamp']) if dc_charging['timestamp'] is not None and ac_charging['timestamp'] is not None else None,
    )
