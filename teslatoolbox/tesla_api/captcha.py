from typing import TypedDict

from twocaptcha import TwoCaptcha

from teslatoolbox.utils.logger import logger

solver = TwoCaptcha('66eeca5e26c19fe495bcfd9342c24949')


def get_captcha_token_for_request_authorization():
    try:
        logger.trace('Getting captcha token...')
        result: CaptchaResult = solver.recaptcha(
            sitekey='6LeezMMZAAAAAKsznpLiPUnIMpnuSP7nNj0YBQis',
            url='https://toolbox.tesla.com/ownerAuthorizationRequests',
        )  # type: ignore
        return result['code']
    except Exception as e:
        logger.error(f'Error getting captcha token: {e}')
        return None


class CaptchaResult(TypedDict):
    captchaId: str
    code: str
