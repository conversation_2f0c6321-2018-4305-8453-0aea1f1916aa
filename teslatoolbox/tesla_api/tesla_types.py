from dataclasses import dataclass
from enum import Enum
import time
from typing import Literal, Never, TypedDict, Optional, Any
from typing_extensions import NotRequired

type Audience = Literal['customer', 'service-fix', 'service', 'factory', 'RMI']


class TokenExpiredError(Exception):
    pass


class NoAuthorizationError(Exception):
    pass


class LimitReachedError(Exception):
    pass


class RequestErrorJSON(TypedDict):
    # {'error': 'Invalid Token', 'type': '400 Bad Request', 'request_id': 'f5674f24-efbe-494a-8e07-b2bfce14aad2'}
    # {'error': 'Token Expired', 'type': '401 Unauthorized', 'request_id': '31420db0-7d0c-421f-802d-a7f8c58a6fde'}
    error: str
    type: str
    request_id: str


class UnixTimestampS(int):
    pass


class UnixTimestampMS(int):
    pass


class Email(str):
    pass


class ISO8601(str):
    pass


class VIN(str):
    pass


class Auth(TypedDict):
    status: Literal['Approved', 'Pending', 'Rejected', 'Expired']
    requestor: Literal['<EMAIL>']
    reason: Literal['Remote diagnostics']
    customer_email: Email
    created_at: ISO8601
    expires: ISO8601
    vin: VIN
    updated_at: ISO8601


class AlertMetadata(TypedDict):
    alert_name: str
    audience: list[Audience]
    clearCondition: str
    description: str
    isLatching: bool
    isUrgent: bool
    potentialImpact: str
    safetyReason: str
    setCondition: str
    toolbox_article_id: str
    user_text: str
    user_text2: str
    lastTimeStamp: int
    count: int


class SignalMetadata(TypedDict):
    audience: list[Audience]
    sig_name: str


class Alert(TypedDict):
    metadata: AlertMetadata
    is_sna: list[bool]
    sig_name: list[str]
    sig_text: list[str]
    sig_value: list[int | float]
    timestamps: list[UnixTimestampMS]


class Signal(TypedDict):
    metadata: SignalMetadata
    is_sna: list[bool]
    sig_text: list[str]
    sig_value: list[int | float]
    timestamps: list[UnixTimestampMS]


class LogInterval(TypedDict):
    start: UnixTimestampMS
    end: UnixTimestampMS


class LogAvailability(TypedDict):
    datasets: list[Never]
    intervals: list[LogInterval]


class HandshakeParameters(TypedDict):
    wifi_wait_until: int
    status_squelch: NotRequired[str]
    use_hermes_stream: NotRequired[str]
    download_retries: NotRequired[int]
    modules_to_skip: NotRequired[str]
    soc_check: NotRequired[bool]
    soc_threshold: NotRequired[int]
    force_apeb: NotRequired[bool]
    verify_gwxfer_write: NotRequired[str]
    mask_ui_notify: NotRequired[str]


class JobDetails(TypedDict):
    reported_success_to_ui: NotRequired[bool]


class Package(TypedDict):
    id: int
    name: str
    signature: str
    location: str
    vehicle_hardware_configuration_id: Optional[int]
    created_at: int
    updated_at: int
    build_id: int
    handshake_parameters: Optional[dict]
    build_type: str
    release_id: int
    container: str
    tegra_os_sha: str
    md5: str
    num_bytes: int
    jenkins_job_name: str
    cdn_path: str
    crypto_block_size: int
    can_decrypt_full_package: bool
    trial_expiration: Optional[int]
    secondary_package_sigs: Optional[list[str]]
    artifacts: Optional[str]
    is_prod_signed: bool
    branch: str
    signature_blob: Optional[str]
    region: Optional[str]
    signing_domain: str
    build_flavor: Optional[str]
    is_ui_debug: bool
    is_ap_debug: bool
    release_scope: Optional[str]
    encrypted_md5: str
    ape_decrypt: bool
    customer_version: str
    usb_cdn_path: str
    encrypted_location: str
    odin_patch_id: Optional[int]
    encryption_key_id: str
    state: Optional[str]
    zip_location: Optional[str]
    abandoned: bool
    non_deployable_commit_id: Optional[int]
    security_version: Optional[int]
    last_job_at: int
    approval_manifest: Optional[str]
    validations: Optional[str]
    abandon_reason: Optional[str]
    device_allowlist: Optional[str]
    device_blocklist: Optional[str]
    ap_git_hash: Optional[str]
    metadata: Optional[dict]


class Job(TypedDict):
    id: int
    package_id: int
    vehicle_id: int
    state: str
    created_at: int
    updated_at: int
    last_vehicle_activity: Optional[str]
    user_abandoned: bool
    vehicle_succeeded: Optional[bool]
    owner: str
    handshake_parameters: HandshakeParameters
    tag: str
    issues: Optional[str]
    details: JobDetails
    late_failure_type: Optional[str]
    package: Package


class Release(TypedDict):
    id: int
    name: str
    validated: bool
    abandoned: bool
    svn_path: Optional[str]
    svn_revision: Optional[str]
    created_at: int
    updated_at: int
    factory_default: bool
    whitelist: Optional[str]
    blacklist: Optional[str]
    non_deployable_commit_id: Optional[str]


class FirmwareMetadata(TypedDict):
    ap_artifacts: Optional[str]


class Firmware(TypedDict):
    id: int
    name: str
    signature: str
    vehicle_hardware_configuration_id: Optional[int]
    created_at: int
    updated_at: int
    build_id: int
    handshake_parameters: Optional[dict]
    build_type: str
    release_id: int
    container: str
    tegra_os_sha: str
    md5: str
    num_bytes: int
    jenkins_job_name: str
    cdn_path: str
    can_decrypt_full_package: bool
    trial_expiration: Optional[int]
    secondary_package_sigs: list[str]
    artifacts: str
    is_prod_signed: bool
    branch: str
    signature_blob: Optional[str]
    region: Optional[str]
    signing_domain: str
    build_flavor: str
    is_ui_debug: bool
    is_ap_debug: bool
    release_scope: str
    encrypted_md5: str
    ape_decrypt: bool
    customer_version: str
    usb_cdn_path: str
    odin_patch_id: Optional[int]
    encryption_key_id: str
    state: Optional[str]
    zip_location: str
    abandoned: bool
    non_deployable_commit_id: Optional[int]
    security_version: int
    last_job_at: int
    approval_manifest: Optional[str]
    validations: Optional[str]
    abandon_reason: Optional[str]
    device_allowlist: Optional[str]
    device_blocklist: Optional[str]
    ap_git_hash: str
    metadata: FirmwareMetadata
    release: Release
    pre_signed: bool
    device_type: str
    approver_details: list[Any]
    container_requires_signing_approval: bool


class Map(TypedDict):
    id: int
    name: str
    signature: str
    vehicle_hardware_configuration_id: Optional[int]
    created_at: int
    updated_at: int
    build_id: int
    handshake_parameters: Optional[dict]
    build_type: str
    release_id: int
    container: str
    tegra_os_sha: str
    md5: str
    num_bytes: int
    jenkins_job_name: str
    cdn_path: str
    can_decrypt_full_package: bool
    trial_expiration: Optional[int]
    secondary_package_sigs: Optional[str]
    artifacts: Optional[str]
    is_prod_signed: bool
    branch: str
    signature_blob: str
    region: str
    signing_domain: str
    build_flavor: Optional[str]
    is_ui_debug: bool
    is_ap_debug: bool
    release_scope: Optional[str]
    encrypted_md5: str
    ape_decrypt: bool
    customer_version: str
    usb_cdn_path: str
    odin_patch_id: Optional[int]
    encryption_key_id: str
    state: Optional[str]
    zip_location: Optional[str]
    abandoned: bool
    non_deployable_commit_id: Optional[str]
    security_version: Optional[int]
    last_job_at: int
    approval_manifest: Optional[str]
    validations: Optional[str]
    abandon_reason: Optional[str]
    device_allowlist: Optional[str]
    device_blocklist: Optional[str]
    ap_git_hash: Optional[str]
    metadata: Optional[FirmwareMetadata]
    release: Release
    pre_signed: bool
    device_type: str
    approver_details: list[Any]
    container_requires_signing_approval: bool


class VehicleSummary(TypedDict):
    _12VBatteryCurrent: float
    _12VBatteryTemp: float
    _12VBatteryVoltage: float
    APP_decisionTemperature: str
    APP_fisheyeCalibProgress: Optional[float]
    APP_fisheyeCalibrated: Optional[bool]
    APP_fisheyeCamExtPitchCal: Optional[float]
    APP_fisheyeCamExtYawCal: Optional[float]
    APP_lPillarCalibProgress: Optional[float]
    APP_lPillarCalibrated: Optional[bool]
    APP_lPillarCamExtPitchCal: Optional[float]
    APP_lPillarCamExtYawCal: Optional[float]
    APP_lRepeatCalibProgress: Optional[float]
    APP_lRepeatCalibrated: Optional[bool]
    APP_lRepeatCamExtPitchCal: Optional[float]
    APP_lRepeatCamExtYawCal: Optional[float]
    APP_mainCalibProgress: Optional[float]
    APP_mainCalibrated: Optional[bool]
    APP_mainCamExtPitchCal: Optional[float]
    APP_mainCamExtYawCal: Optional[float]
    APP_narrowCalibProgress: Optional[float]
    APP_narrowCalibrated: Optional[bool]
    APP_narrowCamExtPitchCal: Optional[float]
    APP_narrowCamExtYawCal: Optional[float]
    APP_rPillarCalibProgress: Optional[float]
    APP_rPillarCalibrated: Optional[bool]
    APP_rPillarCamExtPitchCal: Optional[float]
    APP_rPillarCamExtYawCal: Optional[float]
    APP_rRepeatCalibProgress: Optional[float]
    APP_rRepeatCalibrated: Optional[bool]
    APP_rRepeatCamExtPitchCal: Optional[float]
    APP_rRepeatCamExtYawCal: Optional[float]
    APS_canMaster: str
    USOE: int
    VAPI_doorState: str
    acc_rail: str
    alert_data: list[list[str]]
    auto_front_door: str
    auto_present_handles: str
    bday: str
    bms_cac_avg: str
    bms_cac_max: str
    bms_cac_min: str
    bms_contactor_fc: str
    bms_contactor_state: str
    bms_current: float
    bms_iso: str
    bms_link_v: str
    bms_max_temp: float
    bms_min_temp: float
    bms_pack_temp: float
    bms_pack_temp_pct: float
    bms_soc_max: str
    bms_soc_min: str
    bms_state: str
    bms_voltage: float
    carver: str
    cd_display_state: int
    cell_ant: str
    cell_apn: str
    cell_bars: int
    cell_model: str
    cell_ps_state: str
    cell_reg: str
    cell_sim_selection: str
    cell_sys_mode: str
    cfg_12v_battery_type: str
    cfg_airsuspension: str
    cfg_audiotype: str
    cfg_autopilot: str
    cfg_car: str
    cfg_chargertype: str
    cfg_connectivitypackage: str
    cfg_country: str
    cfg_driver_assist: str
    cfg_exteriorcolor: str
    cfg_fc_allowed: str
    cfg_folding_mirrors: bool
    cfg_four_wheel_drive: bool
    cfg_frontfog: bool
    cfg_homelink: str
    cfg_memorymirrors: bool
    cfg_navigationmapregion: str
    cfg_other_fc_allowed: bool
    cfg_perf_add_on: str
    cfg_powerliftgate: bool
    cfg_rear_drive_unit: str
    cfg_rearfog: bool
    cfg_spoiler: str
    cfg_sunroof: str
    cfg_towing: str
    cfg_tpms_type: str
    cfg_wheeltype: str
    cfg_xm_antenna: bool
    chg_av_power: Optional[float]
    chg_avail_master: int
    chg_avail_slave: int
    chg_avail_total: int
    chg_cable_curr_limit: float
    chg_current_master: float
    chg_current_slave: float
    chg_current_total: float
    chg_enabled: bool
    chg_evse: int
    chg_fault: bool
    chg_main_state: str
    chg_p1_enabled: str
    chg_p1_fault: str
    chg_p1_linecurrent: str
    chg_p1_mainstate: str
    chg_p1_templega: str
    chg_p1_templegb: str
    chg_p1_vin: str
    chg_p2_enabled: str
    chg_p2_fault: str
    chg_p2_linecurrent: str
    chg_p2_mainstate: str
    chg_p2_templega: str
    chg_p2_templegb: str
    chg_p2_vin: str
    chg_p3_enabled: str
    chg_p3_fault: str
    chg_p3_linecurrent: str
    chg_p3_mainstate: str
    chg_p3_templega: str
    chg_p3_templegb: str
    chg_p3_vin: str
    chg_phases: Optional[int]
    chg_pilot: str
    chg_pilot_c: float
    chg_prox: str
    chg_voltage_master: float
    chg_voltage_slave: float
    chgs_av_power: Optional[float]
    chgs_cable_curr_limit: float
    chgs_enabled: bool
    chgs_fault: bool
    chgs_main_state: str
    chgs_p1_enabled: str
    chgs_p1_fault: str
    chgs_p1_linecurrent: str
    chgs_p1_mainstate: str
    chgs_p1_templega: str
    chgs_p1_templegb: str
    chgs_p1_vin: str
    chgs_p2_enabled: str
    chgs_p2_fault: str
    chgs_p2_linecurrent: str
    chgs_p2_mainstate: str
    chgs_p2_templega: str
    chgs_p2_templegb: str
    chgs_p2_vin: str
    chgs_p3_enabled: str
    chgs_p3_fault: str
    chgs_p3_linecurrent: str
    chgs_p3_mainstate: str
    chgs_p3_templega: str
    chgs_p3_templegb: str
    chgs_p3_vin: str
    child_lock: str
    cp_cover_closed: bool
    cp_door: bool
    cp_door_sensor: bool
    cp_insert_enable: bool
    cp_latch: str
    cp_led_color: str
    cp_type: str
    door_unlock_mode: str
    drive_rail: str
    gear: Optional[int]
    gps_ant: str
    gps_fix_type: int
    hvac_rail: str
    ic_display_state: int
    info_fuse_state: str
    info_hw: str
    is_locked: bool
    key_fob_close_all: str
    language: str
    max_brick_volt: float
    min_brick_volt: float
    nav_map_release: str
    odo: float
    outside_temp: Optional[float]
    passive_entry_enabled: str
    pt_in_act_t: Optional[float]
    pt_in_pass_t: Optional[float]
    range: float
    recent_alerts: list[str]
    remote_service_alert: str
    sats_in_use: int
    showroom_mode: bool
    speed: Optional[float]
    thc_aux_evap_a: str
    thc_aux_evap_sol: str
    thc_aux_evap_t: str
    thc_battpump1_req: Optional[Any]
    thc_battpump2_req: Optional[Any]
    thc_chill_onoff: Optional[bool]
    thc_comp_rpm: Optional[Any]
    thc_disch_pres: Optional[Any]
    thc_disch_pres_target: Optional[Any]
    thc_disch_t: Optional[Any]
    thc_hvac_onoff: Optional[bool]
    thc_pt_in: Optional[Any]
    thc_ptpump2_req: str
    thc_ptpump_req: Optional[Any]
    thc_suct_pres: Optional[Any]
    thc_suct_t: Optional[Any]
    time: str
    tpms_pressure_fl: str
    tpms_pressure_fr: str
    tpms_pressure_rl: str
    tpms_pressure_rr: str
    ui_mode: str
    unlock_on_park: str
    utcoffset: int
    valid_gps: bool
    vin: VIN
    walk_away_lock: str
    wifi_address: str
    wifi_bars: int
    wifi_cellstate: Optional[Any]
    wifi_connstatus: str
    wifi_enabled: bool
    wifi_last_up_time_epoch: UnixTimestampS
    wifi_linkstate: str
    wifi_signal: int
    wifi_wifistate: Literal['WifiUp', 'WifiDown']  # maybe more? only WifiUp is confirmed
    fw_package: Firmware
    map_package: map
    latest_fw: Firmware
    latest_map: map
    job_history: list[Job]
    active_jobs: list[Job]


class VehicleSummaryResponse(TypedDict):
    response: VehicleSummary


class Measurement(TypedDict):
    signal: str
    timestamp: Optional[UnixTimestampMS]


class IntMeasurement(Measurement):
    value: int


class FloatMeasurement(Measurement):
    value: float


BMS_ContactorStateMap: dict[str, str] = {
    'BMS_CTRSET_SNA': 'SNA',
    'BMS_CTRSET_OPEN': 'OPEN',
    'BMS_CTRSET_OPENING': 'OPENING',
    'BMS_CTRSET_CLOSING': 'CLOSING',
    'BMS_CTRSET_CLOSED': 'CLOSED',
    'BMS_CTRSET_WELDED': 'WELDED',
    'BMS_CTRSET_BLOCKED': 'BLOCKED',
}


class CleanVehicleSummary(TypedDict):
    vin: str
    odometer: float  # km
    birthday: time.struct_time
    color: str
    model: str
    time: UnixTimestampS
    BMS_contactorState: Optional[str]
    min_brick_volt: float  # V
    max_brick_volt: float  # V
    BMS_cacMin: Optional[float]  # Ah
    BMS_cacMax: Optional[float]  # Ah
    BMS_cacAvg: Optional[float]  # Ah
    BMS_isolationResistance: Optional[int]  # kOhm
