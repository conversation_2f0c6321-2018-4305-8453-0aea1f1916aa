import time
from sqlalchemy import select, or_
import urllib3

from teslatoolbox.utils.logger import logger
from teslatoolbox.db import CustomerConfirmation, Diagnosis, DiagnosisState, get_db_session
from teslatoolbox.tesla_api.tesla_api import get_auth_list
from teslatoolbox.services.helpers import next_task, fail_diagnosis


def update_all_auth_status():
    with get_db_session() as db:
        waiting_for_auth = db.scalars(
            select(Diagnosis).where(
                or_(
                    Diagnosis.state == DiagnosisState.AUTHORIZATION_PENDING,
                    Diagnosis.state == DiagnosisState.REQUESTING_AUTHORIZATION,
                )
            )
        )
        auth_list = None
        for diagnosis in waiting_for_auth:
            if auth_list is None:
                try:
                    auth_list = get_auth_list()
                except urllib3.exceptions.HTTPError as e:
                    logger.warning(f'Failed to get auth list. {e}')
                    return
                if auth_list is None:
                    logger.error('Failed to get auth list.')
                    return
            vin = diagnosis.customer.vin
            if vin in auth_list.keys():
                if not diagnosis.customer.tesla_mail == auth_list[vin]['customer_email']:
                    continue
                diagnosis.customer.confirmation = CustomerConfirmation.CONFIRMED
                if auth_list[vin]['status'] == 'Approved':
                    next_task(diagnosis, DiagnosisState.GETTING_VEHICLE_ID)
                    logger.info(f'[{diagnosis.id} : {vin}] Authorization approved.')
                elif auth_list[vin]['status'] == 'Rejected' and not diagnosis.state == DiagnosisState.FAILED:
                    fail_diagnosis(diagnosis, 'Authorization rejected.')
                elif auth_list[vin]['status'] == 'Expired' and not diagnosis.state == DiagnosisState.FAILED:
                    fail_diagnosis(diagnosis, 'Authorization expired.')
                elif auth_list[vin]['status'] == 'Pending' and not diagnosis.state == DiagnosisState.AUTHORIZATION_PENDING:
                    # We missed the feedback on the browser side, skipped the requesting authorization step.
                    next_task(diagnosis, DiagnosisState.AUTHORIZATION_PENDING)
                    logger.info(f'[{diagnosis.id} : {vin}] Authorization pending.')
            diagnosis.updated_at = int(time.time())
        db.commit()
