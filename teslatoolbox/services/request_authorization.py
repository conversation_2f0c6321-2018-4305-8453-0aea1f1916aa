import time

from sqlalchemy import or_, select

from teslatoolbox.db import CustomerConfirmation, Diagnosis, DiagnosisState, get_db_session, may_retry
from teslatoolbox.utils.logger import logger
from teslatoolbox.tesla_api.tesla_api import request_auth
from teslatoolbox.services.helpers import fail_diagnosis, next_task


def request_all_authorizations():
    with get_db_session() as db:
        diagnoses = db.scalars(select(Diagnosis).where(or_(Diagnosis.state == DiagnosisState.INIT, Diagnosis.state == DiagnosisState.REQUESTING_AUTHORIZATION)).where(may_retry()))
        for diagnosis in diagnoses:
            logger.trace(f'[{diagnosis.id} : {diagnosis.customer.vin}] Requesting authorization.')
            request_auth_result = request_auth(diagnosis.customer.vin, diagnosis.customer.tesla_mail)
            if request_auth_result == 'success':
                logger.info(f'[{diagnosis.id} : {diagnosis.customer.vin}] Authorization requested.')
                diagnosis.customer.confirmation = CustomerConfirmation.CONFIRMED
                next_task(diagnosis, DiagnosisState.AUTHORIZATION_PENDING)
            elif request_auth_result == 'captcha_error':
                logger.trace(f'[{diagnosis.id} : {diagnosis.customer.vin}] Failed at requesting authorization. Captcha error.')
                diagnosis.error_message = 'Failed at requesting authorization. Captcha error.'
                diagnosis.updated_at = int(time.time())
                diagnosis.retry_after = 0
                diagnosis.current_task_attempts += 1  # purely informational
            elif request_auth_result == 'mail_wrong':
                diagnosis.customer.confirmation = CustomerConfirmation.MAIL_WRONG
                fail_diagnosis(diagnosis, 'Authorization request failed: Specified email does not own vin.')
            elif request_auth_result == 'vin_wrong':
                diagnosis.customer.confirmation = CustomerConfirmation.VIN_WRONG
                fail_diagnosis(diagnosis, 'Authorization request failed: VIN is wrong.')
            db.commit()
