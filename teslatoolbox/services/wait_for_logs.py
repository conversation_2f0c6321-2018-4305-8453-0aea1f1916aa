import time
from sqlalchemy import select

from teslatoolbox.utils.logger import logger
from teslatoolbox.db import Diagnosis, DiagnosisState, get_db_session, may_retry
from teslatoolbox.tesla_api.measurement import get_vehicle_log_availability_fraction
from teslatoolbox.tesla_api.tesla_types import NoAuthorizationError
from teslatoolbox.services.helpers import next_task, fail_diagnosis


def wait_for_logs():
    with get_db_session() as db:
        diagnoses = db.scalars(select(Diagnosis).where(Diagnosis.state == DiagnosisState.WAITING_FOR_LOGS).where(may_retry()))
        for diagnosis in diagnoses:
            logger.trace(f'[{diagnosis.id} : {diagnosis.customer.vin}] Waiting for logs.')
            try:
                log_availability_fraction = get_vehicle_log_availability_fraction(diagnosis.customer.vin)
            except NoAuthorizationError:
                fail_diagnosis(diagnosis, 'No authorization at step waiting for logs.')
                db.commit()
                continue
            diagnosis.logs_available_fraction = log_availability_fraction
            if log_availability_fraction >= 0.7:
                logger.info(f'[{diagnosis.id} : {diagnosis.customer.vin}] Logs adequate: {log_availability_fraction * 100:.0f}%.')
                next_task(diagnosis, DiagnosisState.DIAGNOSING)
                db.commit()
                continue
            logger.trace(f'[{diagnosis.id} : {diagnosis.customer.vin}] Logs not adequate: {log_availability_fraction * 100:.0f}%.')
            diagnosis.retry_after = int(time.time()) + 60 * 10
            diagnosis.updated_at = int(time.time())
            diagnosis.current_task_attempts += 1
            if diagnosis.current_task_attempts >= 7:
                if log_availability_fraction >= 0.3:
                    logger.warning(f'[{diagnosis.id} : {diagnosis.customer.vin}] Waited an hour, will accept {log_availability_fraction * 100:.0f}% logs.')
                    next_task(diagnosis, DiagnosisState.DIAGNOSING)
                else:
                    if diagnosis.current_task_attempts >= 6 * 24:
                        fail_diagnosis(diagnosis, 'Logs not adequate after waiting for a day.')
            db.commit()
