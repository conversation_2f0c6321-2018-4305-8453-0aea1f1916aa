import time
from sqlalchemy import select

from teslatoolbox.utils.logger import logger
from teslatoolbox.db import Diagnosis, DiagnosisState, get_db_session, may_retry
from teslatoolbox.tesla_api.tesla_api import get_vehicle_id
from teslatoolbox.tesla_api.tesla_types import NoAuthorizationError
from teslatoolbox.services.helpers import next_task, fail_diagnosis


def get_all_vehicle_ids():
    with get_db_session() as db:
        diagnoses = db.scalars(select(Diagnosis).where(Diagnosis.state == DiagnosisState.GETTING_VEHICLE_ID).where(may_retry()))
        for diagnosis in diagnoses:
            logger.trace(f'[{diagnosis.id} : {diagnosis.customer.vin}] Getting vehicle id.')
            if diagnosis.customer.vehicle_id is not None:
                logger.info(f'[{diagnosis.id} : {diagnosis.customer.vin}] Vehicle id already known: {diagnosis.customer.vehicle_id}.')
                next_task(diagnosis, DiagnosisState.REQUESTING_LOGS)
                db.commit()
                continue
            db.commit()
            try:
                vehicle_id = get_vehicle_id(diagnosis.customer.vin)
            except NoAuthorizationError:
                fail_diagnosis(diagnosis, 'No authorization at step getting vehicle id.')
                db.commit()
                continue
            if vehicle_id is None:
                logger.info(f'[{diagnosis.id} : {diagnosis.customer.vin}] Vehicle id not found.')
                diagnosis.retry_after = int(time.time()) + 60 * 1
                diagnosis.updated_at = int(time.time())
                diagnosis.current_task_attempts += 1
                if diagnosis.current_task_attempts >= 5:
                    diagnosis.state = DiagnosisState.FAILED
                    diagnosis.updated_at = int(time.time())
                    diagnosis.retry_after = 0
                    diagnosis.current_task_attempts = 0
                    diagnosis.error_message = 'Vehicle not id found after 5 attempts.'
                    logger.warning(f'[{diagnosis.id} : {diagnosis.customer.vin}] Failed. Vehicle id not found after 5 attempts.')
            else:
                logger.info(f'[{diagnosis.id} : {diagnosis.customer.vin}] Vehicle id found: {vehicle_id}.')
                diagnosis.customer.vehicle_id = vehicle_id
                next_task(diagnosis, DiagnosisState.REQUESTING_LOGS)
            db.commit()
