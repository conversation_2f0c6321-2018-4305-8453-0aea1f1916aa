import base64
import time
from pathlib import Path
from sqlalchemy import select, or_

import microsoft.oauth2
from teslatoolbox.utils.logger import logger
from teslatoolbox.db import Diagnosis, DiagnosisState, DiagnosisType, get_db_session, may_retry
from teslatoolbox.pdfreport import create_pdf_report
from teslatoolbox.services.helpers import next_task, fail_diagnosis


def send_all_reports():
    with get_db_session() as db:
        diagnoses = db.scalars(
            select(Diagnosis)
            .where(or_(Diagnosis.state == DiagnosisState.SENDING_REPORT, Diagnosis.state == DiagnosisState.SENDING_FOLLOWUP_REPORT))
            .where(Diagnosis.diagnosis_type == DiagnosisType.FREE_DIAGNOSIS_LIGHT_SHOW)
            .where(may_retry())
            .limit(3)  # limits: 200 per hour, 750 per day
        )
        for diagnosis in diagnoses:
            logger.trace(f'[{diagnosis.id} : {diagnosis.customer.vin}] Sending report.')
            pdf = create_pdf_report(diagnosis)
            if pdf is None:
                logger.error(f'[{diagnosis.id} : {diagnosis.customer.vin}] Failed at generating pdf report.')
                diagnosis.updated_at = int(time.time())
                diagnosis.retry_after = 0
                diagnosis.current_task_attempts = 0
                if diagnosis.state == DiagnosisState.SENDING_REPORT:
                    fail_diagnosis(diagnosis, 'Failed at generating pdf report.')
                else:
                    diagnosis.state = DiagnosisState.DONE
                    diagnosis.error_message += ' Failed at generating follow-up pdf report.'
                db.commit()
                continue
            attachments = [
                {
                    '@odata.type': '#microsoft.graph.fileAttachment',
                    'name': f'report_{diagnosis.customer.vin}.pdf',
                    'contentType': 'application/pdf',
                    'contentBytes': base64.b64encode(pdf).decode('utf-8'),
                }
            ]
            if diagnosis.state == DiagnosisState.SENDING_FOLLOWUP_REPORT:
                success = microsoft.send_mail(
                    from_address='<EMAIL>',
                    to_address=diagnosis.customer.tesla_mail,
                    subject='Data Accuracy Update - Your Feedback Matters',
                    body="""Dear Tesla Light Show participant,<br>
<br>
We have noticed that the data in some of our previous reports was not fully up to date for all users. To ensure the highest level of accuracy, we have implemented a number of improvements and refinements. Our automatic reports are currently in a test phase, and we want to ensure that the data is fully reliable.<br>
<br>
These adjustments include updates to outdated information and refinements to further improve data precision. We appreciate your patience and understanding as we continue to optimize our reporting processes.<br>
<br>
It is important to us that the reports are as precise as possible. Therefore, we kindly ask you to review the data and inform us if you notice any inaccuracies or inconsistencies. Your feedback will help us further fine-tune our reporting process.<br>
<br>
Please do not hesitate to reach out if you have any questions or observations. We greatly appreciate your cooperation and support.<br>
<br>
Best regards,<br>
The Electrify.eu team<br>""",
                    raw_attachments=attachments,
                )
            else:
                success = microsoft.send_mail(
                    from_address='<EMAIL>',
                    to_address=diagnosis.customer.tesla_mail,
                    subject='Your battery diagnostic report',
                    body_html_file=Path('teslatoolbox/static/mail/free_report.html'),
                    raw_attachments=attachments,
                )
            diagnosis.updated_at = int(time.time())
            if not success:
                logger.error(f'[{diagnosis.id} : {diagnosis.customer.vin}] Failed at sending email.')
                diagnosis.retry_after = int(time.time()) + 60 * 30
                diagnosis.current_task_attempts += 1
                if diagnosis.current_task_attempts >= 5:
                    fail_diagnosis(diagnosis, 'Failed after 5 attempts to send email.')
                db.commit()
                continue
            was_state = diagnosis.state
            next_task(diagnosis, DiagnosisState.DONE)
            if was_state == DiagnosisState.SENDING_REPORT:
                logger.info(f'[{diagnosis.id} : {diagnosis.customer.vin}] Report sent.')
                diagnosis.error_message = f'Report sent at {time.strftime("%Y-%m-%d %H:%M:%S")}.'
            else:
                logger.info(f'[{diagnosis.id} : {diagnosis.customer.vin}] Follow-up report sent.')
                diagnosis.error_message = f'Follow-up report sent at {time.strftime("%Y-%m-%d %H:%M:%S")}.'
            db.commit()
