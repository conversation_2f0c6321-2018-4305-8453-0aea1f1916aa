from microsoft import send_mail
from teslatoolbox.db import get_db_session, Customer, CustomerConfirmation
from teslatoolbox.utils.logger import logger

from sqlalchemy import select


def send_all_wrong_vin_mails():
    with get_db_session() as db:
        customers = db.scalars(select(Customer).where(Customer.confirmation == CustomerConfirmation.VIN_WRONG).where(Customer.wrong_details_mail_sent == False))  # noqa: E712
        for customer in customers:
            result = send_wrong_vin_mail(customer)
            db.commit()
            if not result:
                break


def send_wrong_vin_mail(customer: Customer):
    result = send_mail(
        from_address='<EMAIL>',
        to_address=customer.tesla_mail,
        subject='Free battery diagnosis: Wrong VIN',
        body=f"""Dear Tesla Light Show participant,<br>
<br>
You have entered your details on our website to receive a free battery diagnosis, but the VIN you entered is not known.<br>
The VIN you entered: <pre>{customer.vin}</pre>
Please double-check the VIN you entered and try again on our website: <a href="https://www.electrify.eu/en/godmode">https://www.electrify.eu/en/godmode</a>. Make sure to enter the correct e-mail address that is linked to this car in your Tesla account.<br>
<br>
Kind regards,<br>
The Electrify.eu team""",
    )
    if result:
        customer.wrong_details_mail_sent = True
        logger.info(f'[{customer.vin}] Sent wrong VIN mail to {customer.tesla_mail}.')
    else:
        logger.error(f'[{customer.vin}] Failed to send wrong VIN mail to {customer.tesla_mail}.')
    return result


def send_all_wrong_mail_mails():
    with get_db_session() as db:
        customers = db.scalars(select(Customer).where(Customer.confirmation == CustomerConfirmation.MAIL_WRONG).where(Customer.wrong_details_mail_sent == False))  # noqa: E712
        for customer in customers:
            result = send_wrong_mail_mail(customer)
            db.commit()
            if not result:
                break


def send_wrong_mail_mail(customer: Customer):
    result = send_mail(
        from_address='<EMAIL>',
        to_address=customer.tesla_mail,
        subject='Free battery diagnosis: Wrong e-mail address',
        body=f"""Dear Tesla Light Show participant,<br>
<br>
You have entered your details on our website to receive a free battery diagnosis, but the e-mail address you entered is not linked to the VIN you entered.<br>
The e-mail address you entered: <tt>{customer.tesla_mail}</tt> does not match the VIN <tt>{customer.vin}</tt><br>
Please double-check the e-mail address you entered and try again on our website: <a href="https://www.electrify.eu/en/godmode">https://www.electrify.eu/en/godmode</a>. Make sure to enter the correct e-mail address that is linked to this car in your Tesla account.<br>
<br>
Kind regards,<br>
The Electrify.eu team""",
    )
    if result:
        customer.wrong_details_mail_sent = True
        logger.info(f'[{customer.vin}] Sent wrong mail mail to {customer.tesla_mail}.')
    else:
        logger.error(f'[{customer.vin}] Failed to send wrong mail mail to {customer.tesla_mail}.')
    return result


if __name__ == '__main__':
    send_all_wrong_vin_mails()
    send_all_wrong_mail_mails()
