import time
from sqlalchemy import select

from teslatoolbox.utils.logger import logger
from teslatoolbox.db import Diagnosis, DiagnosisState, get_db_session, may_retry
from teslatoolbox.tesla_api.measurement import get_vehicle_log_availability_fraction
from teslatoolbox.tesla_api.tesla_api import request_logs
from teslatoolbox.tesla_api.tesla_types import LimitReachedError, NoAuthorizationError
from teslatoolbox.services.helpers import next_task, fail_diagnosis


def request_all_logs():
    with get_db_session() as db:
        diagnoses = db.scalars(select(Diagnosis).where(Diagnosis.state == DiagnosisState.REQUESTING_LOGS).where(may_retry()))
        for diagnosis in diagnoses:
            logger.trace(f'[{diagnosis.id} : {diagnosis.customer.vin}] Checking initial log availability.')
            log_availability_fraction = get_vehicle_log_availability_fraction(diagnosis.customer.vin)
            diagnosis.logs_available_fraction = log_availability_fraction
            db.commit()
            if log_availability_fraction >= 0.7:
                logger.info(f'[{diagnosis.id} : {diagnosis.customer.vin}] Initial logs already available: {log_availability_fraction * 100:.0f}%.')
                next_task(diagnosis, DiagnosisState.DIAGNOSING)
                db.commit()
                continue
            logger.trace(f'[{diagnosis.id} : {diagnosis.customer.vin}] Requesting logs, only {log_availability_fraction * 100:.0f}% available.')
            try:
                request_logs_result = request_logs(diagnosis.customer.vin)
            except NoAuthorizationError:
                fail_diagnosis(diagnosis, 'No authorization at step requesting logs.')
                db.commit()
                continue
            except LimitReachedError:
                logger.error(f'[{diagnosis.id} : {diagnosis.customer.vin}] Failed at requesting logs. Limit reached.')
                diagnosis.updated_at = int(time.time())
                diagnosis.retry_after = int(time.time()) + 86400
                db.commit()
                continue

            if not request_logs_result:
                logger.info(f'[{diagnosis.id} : {diagnosis.customer.vin}] Failed at requesting logs.')
                diagnosis.error_message = 'Failed at requesting logs.'
                diagnosis.retry_after = int(time.time()) + 60 * 5
                diagnosis.updated_at = int(time.time())
                diagnosis.current_task_attempts += 1
                if diagnosis.current_task_attempts >= 5:
                    fail_diagnosis(diagnosis, 'Failed at requesting logs after 5 attempts.')
                db.commit()
                continue
            logger.info(f'[{diagnosis.id} : {diagnosis.customer.vin}] Logs requested. {log_availability_fraction * 100:.0f}% available.')
            next_task(diagnosis, DiagnosisState.WAITING_FOR_LOGS)
            db.commit()
