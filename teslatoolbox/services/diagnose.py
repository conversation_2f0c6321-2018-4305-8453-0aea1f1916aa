import time
from calendar import timegm
from sqlalchemy import select, or_
from sqlalchemy.orm.attributes import flag_modified

from teslatoolbox.utils.dict_combiner import is_any_none, combine_dicts
from teslatoolbox.utils.logger import logger
from teslatoolbox.db import Diagnosis, DiagnosisState, get_db_session, may_retry
from teslatoolbox.tesla_api.measurement import cleanup_vehicle_summary
from teslatoolbox.tesla_api.report import createReport
from teslatoolbox.tesla_api.tesla_api import get_vehicle_summary, poke_vehicle
from teslatoolbox.tesla_api.tesla_types import CleanVehicleSummary, NoAuthorizationError
from teslatoolbox.services.helpers import next_task, fail_diagnosis


def diagnose_all():
    with get_db_session() as db:
        diagnoses = db.scalars(select(Diagnosis).where(or_(Diagnosis.state == DiagnosisState.WAKING_VEHICLE, Diagnosis.state == DiagnosisState.DIAGNOSING)).where(may_retry()))
        for diagnosis in diagnoses:
            logger.trace(f'[{diagnosis.id} : {diagnosis.customer.vin}] Diagnosing.')
            if diagnosis.vitals is None or is_any_none(diagnosis.vitals):
                if diagnosis.state == DiagnosisState.WAKING_VEHICLE:
                    try:
                        awake = poke_vehicle(diagnosis.customer.vin)
                    except NoAuthorizationError:
                        fail_diagnosis(diagnosis, 'No authorization at step waking vehicle.')
                        db.commit()
                        continue
                    if not awake:
                        logger.warning(f'[{diagnosis.id} : {diagnosis.customer.vin}] Vehicle is not awake.')
                        diagnosis.error_message = 'Vehicle is not awake.'
                        diagnosis.retry_after = int(time.time()) + 60 * 60
                        diagnosis.updated_at = int(time.time())
                        diagnosis.current_task_attempts += 1
                        if diagnosis.current_task_attempts >= 48:
                            logger.warning(f'[{diagnosis.id} : {diagnosis.customer.vin}] Could not awake vehicle after 48 hours. Trying to continue anyway.')
                        db.commit()
                        continue
                    next_task(diagnosis, DiagnosisState.DIAGNOSING)
                    db.commit()
                if diagnosis.vitals is None:
                    logger.trace(f'[{diagnosis.id} : {diagnosis.customer.vin}] Requesting summary.')
                else:
                    logger.trace(f'[{diagnosis.id} : {diagnosis.customer.vin}] Requesting missing summary parts.')
                try:
                    summary = get_vehicle_summary(diagnosis.customer.vehicle_id)
                except NoAuthorizationError:
                    fail_diagnosis(diagnosis, 'No authorization at step getting summary.')
                    db.commit()
                    continue
                if summary is None and diagnosis.vitals is None:
                    logger.info(f'[{diagnosis.id} : {diagnosis.customer.vin}] Got no summary.')
                    diagnosis.error_message = 'No summary.'
                    diagnosis.retry_after = int(time.time()) + 60 * 5
                    diagnosis.updated_at = int(time.time())
                    diagnosis.current_task_attempts += 1
                    if diagnosis.current_task_attempts >= 5:
                        fail_diagnosis(diagnosis, 'Got no summary after 5 attempts.')
                    db.commit()
                    continue
                elif diagnosis.vitals is None:
                    diagnosis.current_task_attempts = 0  # first time we got vitals: reset retry counter
                if summary is not None:
                    vitals = cleanup_vehicle_summary(summary)
                    if is_any_none(vitals) and diagnosis.vitals is not None:
                        vitals: CleanVehicleSummary = combine_dicts(vitals, diagnosis.vitals)  # type: ignore
                    diagnosis.vitals = vitals
                    flag_modified(diagnosis, 'vitals')
                    if vitals['color'] is not None:
                        diagnosis.customer.color = vitals['color']
                        flag_modified(diagnosis.customer, 'color')
                    if vitals['model'] is not None:
                        diagnosis.customer.model = vitals['model']
                        flag_modified(diagnosis.customer, 'model')
                    if vitals['birthday'] is not None:
                        diagnosis.customer.birthday = timegm(tuple(vitals['birthday']))
                        flag_modified(diagnosis.customer, 'birthday')
                        timegm(tuple(diagnosis.vitals['birthday']))
                    # FIXME: delete color, model, birthday from vitals
                    db.commit()

            if diagnosis.report is None or is_any_none(diagnosis.report):
                if diagnosis.report is None:
                    logger.trace(f'[{diagnosis.id} : {diagnosis.customer.vin}] Generating measurements report.')
                else:
                    logger.trace(f'[{diagnosis.id} : {diagnosis.customer.vin}] Generating missing measurements report parts.')
                try:
                    report = createReport(diagnosis.vitals)
                except NoAuthorizationError:
                    logger.info(f'[{diagnosis.id} : {diagnosis.customer.vin}] Failed at generating report. No authorization.')
                    fail_diagnosis(diagnosis, 'No authorization at step generating report.')
                    db.commit()
                    continue

                if is_any_none(report) and diagnosis.report is not None:
                    report = combine_dicts(report, diagnosis.report)

                diagnosis.report = report
                flag_modified(diagnosis, 'report')
                db.commit()

            if is_any_none(diagnosis.report):
                logger.info(f'[{diagnosis.id} : {diagnosis.customer.vin}] Report still missing values.')
                diagnosis.retry_after = int(time.time()) + 60 * 60
                diagnosis.updated_at = int(time.time())
                diagnosis.current_task_attempts += 1
                if diagnosis.current_task_attempts >= 5:
                    fail_diagnosis(diagnosis, 'Could not generate report values after 5 attempts.')
                db.commit()
                continue

            next_task(diagnosis, DiagnosisState.SENDING_REPORT)
            logger.info(f'[{diagnosis.id} : {diagnosis.customer.vin}] Diagnosed.')
            db.commit()
