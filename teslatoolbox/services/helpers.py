import time
from teslatoolbox.db import DiagnosisState, Diagnosis
from teslatoolbox.utils.logger import logger


def next_task(diagnosis: Diagnosis, task: DiagnosisState):
    """
    Transition a diagnosis to the next task state after a successful operation.

    Args:
        diagnosis: The diagnosis to update
        task: The next DiagnosisState to transition to
    """
    diagnosis.state = task
    diagnosis.updated_at = int(time.time())
    diagnosis.error_message = None
    diagnosis.current_task_attempts = 0
    diagnosis.retry_after = 0


def fail_diagnosis(diagnosis: Diagnosis, error_message: str):
    """
    Immediately fail a diagnosis with the given error message.

    Args:
        diagnosis: The diagnosis to fail
        error_message: The error message to set
    """
    diagnosis.state = DiagnosisState.FAILED
    diagnosis.error_message = error_message
    diagnosis.updated_at = int(time.time())
    diagnosis.retry_after = 0
    diagnosis.current_task_attempts = 0
    logger.warning(f'[{diagnosis.id} : {diagnosis.customer.vin}] Failed. {error_message}')


def track_failure(diagnosis: Diagnosis, error_message: str, retry_delay_seconds: int, max_attempts: int, fail_on_max_attempts: bool = True) -> bool:
    """
    Track a failure for a diagnosis, handle retry logic, and set failure state if needed.

    Args:
        diagnosis: The diagnosis record to update
        error_message: Error message to log
        retry_delay_seconds: Time to wait before next retry attempt
        max_attempts: Maximum number of attempts before setting failure state
        fail_on_max_attempts: If True, transition to FAILED state upon max attempts

    Returns:
        True if max attempts reached, False otherwise
    """
    diagnosis.error_message = error_message
    diagnosis.retry_after = int(time.time()) + retry_delay_seconds
    diagnosis.updated_at = int(time.time())
    diagnosis.current_task_attempts += 1

    max_attempts_reached = diagnosis.current_task_attempts >= max_attempts

    if max_attempts_reached and fail_on_max_attempts:
        failure_message = f'{error_message} after {max_attempts} attempts.'
        fail_diagnosis(diagnosis, failure_message)

    return max_attempts_reached
