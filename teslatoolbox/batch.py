import time

from sqlalchemy.sql.expression import or_, select, func
from teslatoolbox.db import Diagnosis, DiagnosisState, may_retry, get_db_session
from sqlalchemy.orm.attributes import flag_modified

from teslatoolbox.utils.dict_combiner import is_any_none, combine_dicts
from teslatoolbox.utils.logger import logger
from teslatoolbox.tesla_api.measurement import can_soft_max_brick_voltage_discrepancy, cleanup_vehicle_summary
from teslatoolbox.tesla_api.tesla_api import get_vehicle_summary, poke_vehicle
from teslatoolbox.tesla_api.tesla_types import NoAuthorizationError


with get_db_session() as db:
    diagnoses = db.scalars(
        select(Diagnosis)
        # .where(
        #     or_(
        #         # func.json_extract(Diagnosis.vitals, '$.BMS_cacAvg').is_(None),
        #         Diagnosis.id > 26,
        #     )
        # )
        # .where(may_retry())
        # .where(func.json_extract(Diagnosis.vitals, '$.time') < 1740610800)
        .where(Diagnosis.id > 116)
    )
    for diagnosis in diagnoses:
        try:
            if diagnosis.report is not None and 'deltaV' in diagnosis.report:
                logger.trace(f'[{diagnosis.id} : {diagnosis.customer.vin}] Getting new delta V.')
                delta_v = can_soft_max_brick_voltage_discrepancy(diagnosis.customer.vin)
                if delta_v is None:
                    logger.info(f'[{diagnosis.id} : {diagnosis.customer.vin}] Failed to get new delta V.')
                    continue
                diagnosis.report['deltaV'] = delta_v
                diagnosis.updated_at = time.time()
                flag_modified(diagnosis, 'report')
                db.commit()
                logger.info(f'[{diagnosis.id} : {diagnosis.customer.vin}] Updated diagnosis with new delta V.')

        except NoAuthorizationError:
            logger.info(f'[{diagnosis.id} : {diagnosis.customer.vin}] No authorization to get vehicle summary.')
            continue

