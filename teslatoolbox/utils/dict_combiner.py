from teslatoolbox.tesla_api.report import Report
from teslatoolbox.tesla_api.tesla_types import CleanVehicleSummary


def is_any_none(dict: dict | CleanVehicleSummary | Report):
    # FIXME: if dict is a CleanVehicleSummary or Report, check all wanted keys instead of present keys, a missing key is also None
    return any(value is None for value in dict.values())


def combine_dicts(dict1: dict | CleanVehicleSummary | Report, dict2: dict | CleanVehicleSummary | Report):
    for key, value in dict1.items():
        if value is None:
            if key in dict2:
                dict1[key] = dict2[key]
    return dict1
