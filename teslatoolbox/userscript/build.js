const esbuild = require('esbuild');
const fs = require('fs');
const JavaScriptObfuscator = require('javascript-obfuscator');
const dictionary = require('./dictionary.js');

// Userscript metadata template
const userscriptHeader = `// ==UserScript==
// @name         Tesla Toolbox Scraper
// @namespace    http://ee.rc5.be/script.user.js
// @version      0.0.1
// @description  Tesla Toolbox Scraper
// @downloadURL  http://ee.rc5.be/script.user.js
// @match        https://toolbox.tesla.com/*
// @match        https://auth.tesla.com/oauth2/v1/authorize?client_id=toolboxvin&response_type=code&*
// @noframes
// @grant        GM_xmlhttpRequest
// @inject-into  content
// @connect      ee.rc5.be
// ==/UserScript==

`;

async function build() {
  // Build the TypeScript files
  const result = await esbuild.build({
    entryPoints: ['src/index.ts'],
    bundle: true,
    minify: false, // Debug: false, Production: true // https://obfuscator.io/
    treeShaking: true, // Debug: false, Production: true
    format: 'iife', // Immediately Invoked Function Expression
    target: ['chrome58', 'firefox57'], // Adjust based on your browser support needs
    write: false, // We'll handle the output ourselves
  });

  // Get the bundled code
  const bundledCode = result.outputFiles[0].text;

  // Combine userscript header with bundled code
  const finalOutput = userscriptHeader + bundledCode;

  // Write to output file
  fs.writeFileSync('dist/script.user.js', finalOutput);

  // Obfuscate the output
  const obfuscatedOutput = JavaScriptObfuscator.obfuscate(finalOutput, {
    compact: true,
    controlFlowFlattening: false,
    controlFlowFlatteningThreshold: 1,
    deadCodeInjection: false,
    deadCodeInjectionThreshold: 1,
    disableConsoleOutput: false,
    identifierNamesGenerator: 'mangled-shuffled',
    // identifierNamesGenerator: 'dictionary',
    identifiersDictionary: dictionary,
    log: false,
    renameGlobals: false,
    renameProperties: true,
    renamePropertiesMode: 'safe',
    rotateStringArray: true,
    seed: 1337,
    selfDefending: true,
    stringArray: true,
    stringArrayCallsTransform: true,
    stringArrayRotate: true,
    stringArrayShuffle: true,
    stringArrayEncoding: ['base64', 'rc4'],
    stringArrayThreshold: 1,
    reservedNames: ['nocache', 'Content-Type', 'token'],
    unicodeEscapeSequence: false,
  });

  // Write obfuscated output to file
  fs.writeFileSync('dist/script.obvs.user.js', userscriptHeader + obfuscatedOutput.getObfuscatedCode());
}

build()//.catch(() => process.exit(1));
