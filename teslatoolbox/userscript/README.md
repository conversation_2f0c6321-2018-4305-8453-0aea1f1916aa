# Tesla Toolbox Scraper UserScript

## Installation & Usage
Use a browser with the folling extensions:
- ublock origin
- Violentmonkey
- Captcha Solver by 2Captcha
  - Enable automatic retry and sumbmit
  - Add Autosubmit rule for `toolbox.tesla.com/ownerAuthorizationRequests`:
    ```
    {"type":"source","value":"document"}
    {"type":"method","value":"querySelector","args":["button[data-test='submit-button']"]}
    {"type":"method","value":"click"}
    ```

Run `npm run build`, then run `python server.py` and open the page http://ee.rc5.be/script.user.js. This will install the userscript in Violentmonkey.

## Development
Compile with `npm run build`.

To get auto reload, run `npm run watch` and `python server.py` at the same time, then enable userscript tracking in Violentmonkey at the page http://ee.rc5.be/script.user.js.
