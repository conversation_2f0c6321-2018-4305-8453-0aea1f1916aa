const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, 'value')?.set;

export function type(element: HTMLInputElement, text: string) {
  element.focus();
  nativeInputValueSetter!.call(element, text);
  const event = new Event('input', { bubbles: true });
  element.dispatchEvent(event);
}

export function click(element: HTMLElement) {
  element.dispatchEvent(new MouseEvent("mousedown", { bubbles: true, button: 0 }));
}
