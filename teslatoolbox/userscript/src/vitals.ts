import { vitals_json } from "./teslatypes";
import { getByText, getByStartingText } from "./xpath";

function getRawVitalsData(): vitals_json {
  let showJsonButton = getByText('Show JSON', 'a');
  if (!showJsonButton) {
    throw new Error('Could not find Show JSON button');
  }
  showJsonButton.click();


  let vitals_data: vitals_json;
  let pre = getByStartingText('{\n  "12V', 'pre');
  if (!pre) {
    throw new Error('Could not find pre element with vitals data');
  }
  let text: string | null = pre.textContent;
  if (!text) {
    throw new Error('Could not find text content in pre element');
  }
  vitals_data = JSON.parse(text);
  console.log(vitals_data);

  let closeButton = getByText('Close', 'button');
  if (!closeButton) {
    throw new Error('Could not find Close button');
  }
  closeButton!.click();

  return vitals_data;
}

export function getVitalsData() {
  let vitals_data = getRawVitalsData();
  return {
    odometer: vitals_data.odo * 1.60934, // convert to km
    birthday: vitals_data.bday,
    min_brick_volt: vitals_data.min_brick_volt,
    max_brick_volt: vitals_data.max_brick_volt,
    cac_min: vitals_data.bms_cac_min === "--" ? null : parseFloat(vitals_data.bms_cac_min),
    cac_max: vitals_data.bms_cac_max === "--" ? null : parseFloat(vitals_data.bms_cac_max),
  }
}
