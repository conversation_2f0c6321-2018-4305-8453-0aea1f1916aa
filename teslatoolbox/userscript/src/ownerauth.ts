import { click, type } from "./human";
import sleep from "./sleep";
import { $x, wait$x, getByText, waitGetByText, getByStartingText } from "./xpath";

export async function requestOwnerAuth(customerEmail: string, vin: string): Promise<{ success: boolean, report: boolean, message: string }> {
  while (closeModalIfPresent()) {
    await sleep(5000);
  }
  const requestButton = getByText('Request Owner Authorization', 'button') as HTMLButtonElement | null;
  if (!requestButton) {
    console.error('[AUTOMATION] Could not find request button on auth page.');
    return { success: false, report: false, message: 'Could not find request button on auth page.' };
  }
  requestButton.click();
  await waitGetByText('Request Owner Authorization', 'button');
  await sleep(Math.random() * 3000);
  const emailField = $x('//input[@placeholder="Enter customer email"]') as HTMLInputElement | null;
  const vinField = $x('//input[@placeholder="Enter customer VIN"]') as HTMLInputElement | null;
  const reasonField = $x('//div[text()="Request Reason:"]/following-sibling::div/div/div');
  const submitButton = getByText('Submit Owner Authorization Request') as HTMLButtonElement | null;
  if (!emailField || !vinField || !reasonField || !submitButton) {
    console.error('[AUTOMATION] Could not find all necessary fields on auth page.');
    console.log(emailField, vinField, reasonField, submitButton);
    await sleep(1000);
    closeModalIfPresent();
    return { success: false, report: false, message: 'Could not find all necessary fields on auth page.' };
  }
  type(emailField, customerEmail);
  await sleep(Math.random() * 1000 + 500);
  type(vinField, vin);
  await sleep(Math.random() * 1000 + 500);
  click(reasonField);
  await sleep(Math.random() * 1000 + 500);
  let remoteDiagnosticsOption = $x('//div[contains(@class, "Select-option")][text()="Remote diagnostics"]');
  if (!remoteDiagnosticsOption) {
    console.error('[AUTOMATION] Could not find Remote diagnostics option in reason field.');
    await sleep(1000);
    closeModalIfPresent();
    return { success: false, report: false, message: 'Could not find Remote diagnostics option in reason field.' };
  }
  click(remoteDiagnosticsOption);
  // await sleep(Math.random() * 2000 + 20000);
  // let captchaFailed;
  // let maxRetries = 10;
  // do {
  //   submitButton.disabled = false;
  //   submitButton.click();
  //   await sleep(Math.random() * 2000 + 2000);
  //   captchaFailed = $x('//*[text()="Failed captcha: Owner Authorization Request"]');
  //   if (captchaFailed) {
  //     console.warn('[AUTOMATION] Captcha failed, retrying...');
  //     await sleep(Math.random() * 2000 + 15000);
  //     maxRetries--;
  //     if (maxRetries <= 0) {
  //       console.error('[AUTOMATION] Too many retries, giving up.');
  //       return false;
  //     }
  //   } else {
  //     console.log('[AUTOMATION] Request submitted.');
  //     await sleep(10000);
  //   }
  // } while (captchaFailed !== null);

  // if submitbutton still disabled: data is wrong, abort→failed

  if (submitButton.disabled) {
    console.error('[AUTOMATION] Submit button is disabled, data is wrong.');
    await sleep(1000);
    closeModalIfPresent();
    return { success: false, report: true, message: 'VIN is wrong.' };
  }

  let maxWait1 = 120 * 5;
  while (maxWait1 > 0) {
    maxWait1--;
    let submitButton = getByText('Submit Owner Authorization Request') as HTMLButtonElement | null;
    if (!submitButton) {
      break;
    }
    await sleep(200);
  }

  let maxWait2 = 30;
  let result = null;
  while (result === null && maxWait2 > 0) {
    maxWait2--;
    await sleep(500);

    if (getByStartingText('Failed Captcha')) {
      result = { success: false, report: true, message: 'Failed Captcha' };
    } else if (getByStartingText('Failed to check email vin ownership')) {
      result = { success: false, report: false, message: 'Failed to check' };
    } else if (getByStartingText('Specified email:')) { // "Specified email: <EMAIL> does not own vin: 5YJSA7E43KF343125: Owner Authorization Request"
      result = { success: false, report: true, message: 'Specified email does not own vin' };
    } else if (getByText('Owner Authorization Request sent')) {
      result = { success: true, report: true, message: '' };
    }

  }

  await sleep(1000);

  closeModalIfPresent();
  if (result === null) {
    console.error('[AUTOMATION] Request failed, no result.');
    result = { success: false, report: false, message: 'Request failed, no result.' };
  }
  return result;
}

function closeModalIfPresent() {
  const closeButton = $x('//div[text()="Customer Email:"]/parent::*/parent::*/parent::*/parent::*/parent::*//i[contains(@class,"modalHeaderClose")]') as HTMLInputElement | null;
  if (closeButton) {
    console.error('[AUTOMATION] Request failed, modal still present. Closing...');
    closeButton.click();
    return true;
  }
  return false;
}
