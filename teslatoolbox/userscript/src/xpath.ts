import sleep from "./sleep";

export const $x = (xpath: string, context: Document = document): HTMLElement | null => {
  const nodes: Node[] = $x_all(xpath, context);
  if (nodes.length > 1) {
    console.warn(`$x('${xpath}') returned multiple elements. Returning the first one.`);
  } else if (nodes.length === 0) {
    return null;
  }
  return nodes[0] as HTMLElement;
}

const $x_all = (xpath: string, context: Document = document) => {
  const iterator = document.evaluate(xpath, context, null, XPathResult.UNORDERED_NODE_ITERATOR_TYPE, null);
  const nodes: Node[] = [];
  let node: Node | null;
  while (node = iterator.iterateNext()) {
    nodes.push(node);
  }
  return nodes;
}

export const wait$x = async (xpath: string, timeout = 10000, context: Document = document): Promise<HTMLElement | null> => {
  const start = Date.now();
  let node: HTMLElement | null;
  while (!(node = $x(xpath, context))) {
    if (Date.now() - start > timeout) {
      // throw new Error(`Timeout waiting for element matching xpath: ${xpath}`);
      return null;
    }
    await sleep(100);
  }
  return node;
}

export const getByText = (text: string, tag = '*') => $x(`//${tag}[text()='${text}']`);
export const getByStartingText = (text: string, tag = '*') => $x(`//${tag}[starts-with(text(),'${text}')]`);

export const waitGetByText = (text: string, tag = '*') => wait$x(`//${tag}[text()='${text}']`);
export const waitGetByStartingText = (text: string, tag = '*') => wait$x(`//${tag}[starts-with(text(),'${text}')]`);
