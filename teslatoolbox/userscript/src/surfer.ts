import { $x, wait$x, getByText, waitGetByText, getByStartingText } from "./xpath";
import { username, password, getTOTP } from "./credetials";
import { type } from "./human";
import sleep from "./sleep";



class Surfer {
  base_location: string;

  constructor() {
    this.base_location = 'https://toolbox.tesla.com/';
  }

  refresh() {
    window.location.href = this.base_location;
  }

  async abort(message: string) {
    if (message) {
      console.error(message);
    } else {
      console.error('Something went wrong.');
    }
    console.log('Reloading in 30s...');
    await sleep(30000);
    this.refresh();
  }

  async isLoggedIn(timeout: number = 30000): Promise<boolean> {
    if (!window.location.href.startsWith(this.base_location)) {
      return false;
    }
    let loggedIn = new Promise<boolean>(async (resolve, reject) => {
      let userProfileButton = await wait$x('//*[@id="tds-site-header"]//button[@aria-label="User"]/*[local-name() = "svg"]', timeout);
      if (userProfileButton !== null && userProfileButton.classList.contains('tds-icon-person')) {
        let expireWaring = getByStartingText('Authentication token will expire');
        if (expireWaring !== null) {
          resolve(false);
        }
        let loggedOutAlert = getByText('Authentication token has expired, or you have logged out. Click here to reauthenticate.');
        if (loggedOutAlert !== null) {
          resolve(false);
        };
        resolve(true);
      }
      resolve(false);
    });
    let loggedOut = new Promise<boolean>(async (resolve, reject) => {
      let signInText = await waitGetByText('SIGN IN TO CONTINUE');
      if (signInText !== null) {
        resolve(false);
      }
      reject(); // wait for the logged in promise to resolve
    });

    return await Promise.any([loggedIn, loggedOut]).then((value) => {
      return value;
    }).catch(() => {
      return false;
    });
  }

  async logIn(): Promise<boolean> {
    // The page can be reloaded anywhere in the process, so we should detect where we are and act accordingly
    if (await this.isLoggedIn()) {
      return true;
    }

    await sleep(Math.random() * 1000 + 5000);
    console.debug('[AUTOMATION] Logging in...');
    if (window.location.href.startsWith(this.base_location)) {
      if (await waitGetByText('SIGN IN TO CONTINUE') === null) {
        this.abort('[AUTOMATION] Could not find sign in page.');
        return false;
      }
      const usernameField = await wait$x('//input[@autocomplete="username"]') as HTMLInputElement | null;
      if (!usernameField) {
        this.abort('[AUTOMATION] Could not find username field.');
        return false;
      }
      console.debug('[AUTOMATION] Filling in username in first page.');
      type(usernameField, username);
      await sleep(Math.random() * 1000 + 500);
      const nextButton = getByText('Next', 'button') as HTMLButtonElement | null;
      if (!nextButton) {
        this.abort('[AUTOMATION] Could not find next button.');
        return false;
      }
      if (nextButton.disabled) {
        this.abort('[AUTOMATION] Next button is disabled, we are doing something wrong.');
        return false;
      }
      console.debug('[AUTOMATION] Clicking next button on first page.');
      nextButton.click();
      // This should take us to https://auth.tesla.com/oauth2/v1/authorize?client_id=toolboxvin&response_type=code...., if not, reload
    } else if (window.location.href.startsWith('https://auth.tesla.com/oauth2/v1/authorize')) {
      // input id=form-input-identity
      const usernameField = wait$x('//input[@id="form-input-identity"]') as Promise<HTMLInputElement | null>;
      const passwordField = wait$x('//input[@type="password"]') as Promise<HTMLInputElement | null>;
      const totpField = wait$x('//input[@maxlength=6]') as Promise<HTMLInputElement | null>;
      Promise.any([usernameField, passwordField, totpField]).then(async (inputElement) => {
        // test which one is available, only one will be available at a time
        if (inputElement === null) {
          this.abort('[AUTOMATION] Could not find username or password field on auth page.');
          return;
        }
        if (inputElement.id === 'form-input-identity') {
          // username
          console.debug('[AUTOMATION] Filling in username in auth page.');
          type(inputElement, username);
          await sleep(Math.random() * 1000 + 500);
          const nextButton = getByText('Next', 'button') as HTMLButtonElement | null;
          if (!nextButton) {
            this.abort('[AUTOMATION] Could not find next button on auth page.');
            await sleep(Math.random() * 30000);
            return;
          }
          if (nextButton.disabled) {
            this.abort('[AUTOMATION] Next button on auth page is disabled, we are doing something wrong.');
            await sleep(Math.random() * 30000);
          }
          console.debug('[AUTOMATION] Clicking next button on auth page.');
          nextButton.click();
        } else if (inputElement.type === 'password') {
          // password
          console.debug('[AUTOMATION] Filling in password in auth page.');
          type(inputElement, atob(password));
          await sleep(Math.random() * 1000 + 500);
          const loginButton = getByText('Sign In', 'button') as HTMLButtonElement | null;
          if (!loginButton) {
            this.abort('[AUTOMATION] Could not find login button on auth page.');
            return false;
          }
          if (loginButton.disabled) {
            this.abort('[AUTOMATION] Login button on auth page is disabled, we are doing something wrong.');
            return false;
          }
          console.debug('[AUTOMATION] Clicking login button on auth page.');
          loginButton.click();
        } else if (inputElement.maxLength === 6) {
          // TOTP
          console.debug('[AUTOMATION] Filling in TOTP in auth page.');
          type(inputElement, getTOTP());
          await sleep(Math.random() * 100 + 100);
          const loginButton = getByText('Submit', 'button') as HTMLButtonElement | null;
          if (!loginButton) {
            this.abort('[AUTOMATION] Could not find submit button on auth page.');
            return false;
          }
          if (loginButton.disabled) {
            this.abort('[AUTOMATION] Submit button on auth page is disabled, we are doing something wrong.');
            return false;
          }
          console.debug('[AUTOMATION] Clicking submit button on auth page.');
          loginButton.click();
        }
      }).catch((error) => {
        this.abort('[AUTOMATION] Could not find username or password field on auth page.');
      });
    } else {
      this.abort('[AUTOMATION] Unexpected page.');
    }
    console.log('[AUTOMATION] Fell out of login procedure.');
    return false;
  }

  showMenu() {
    let menu = $x('//*[@id="tds-site-header"]/div[1]/button');
    if (!menu) {
      throw new Error('Could not find menu button');
    }
    menu.click();
  }

  async garage() {
    if (window.location.href === this.base_location + 'tools/garage') {
      return;
    }
    let link = $x('//*[@id="Garage"]');
    if (!link) {
      this.showMenu();
    }
    $x('//*[@id="Garage"]')!.click();
    await waitGetByText("Show JSON", "a");
  }

  async auth_panel() {
    if (window.location.href === this.base_location + 'ownerAuthorizationRequests') {
      return;
    }
    let link = $x('//*[@id="Owner Authorization Request"]');
    if (!link) {
      this.showMenu();
    }
    $x('//*[@id="Owner Authorization Request"]')!.click();
    await waitGetByText("Request Owner Authorization", "button");
  }
}

const surf = new Surfer();
export default surf;
