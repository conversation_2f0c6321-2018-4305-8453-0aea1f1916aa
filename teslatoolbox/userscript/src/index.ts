import { getVitalsData } from "./vitals";
import surf from "./surfer";
import { requestOwnerAuth } from "./ownerauth";
import client from "./client";
import { getTbxToken } from "./credetials";
import sleep from "./sleep";

(async () => {
  console.log('Start!');
  console.log('Logged in:', await surf.isLoggedIn());
  const loggedIn = await surf.logIn();
  if (!loggedIn) {
    console.error('Failed to log in');
    return;
  }
  await client.wait_online();
  console.log('Connected to server');
  client.postTbxToken(getTbxToken()!);
  await surf.auth_panel();
  await sleep(5000);
  try {
    while (true) {
      await surf.logIn();
      let task = await client.getTask();
      if (task.type === 'wait') {
        await sleep(task.seconds * 1000);
        continue;
      } else if (task.type === 'send_tbx_token') {
        // we probably need to login again; refresh page
        surf.refresh();
        continue;
      } else if (task.type === 'request_authorization') {
        let success = await requestOwnerAuth(task.tesla_mail, task.vin);
        if (success.report) {
          await client.postRequestAutorizationStatus(task.id, success.success, success.message);
        }
      } else {
        console.error('Unknown task type:', task);
        await sleep(60000);
      }
      await sleep(50);
    }
  } catch (e) {
    console.error('Error:', e);
    console.log('Reloading in 30s...');
    await sleep(30000);
    surf.refresh();
  }

  // await surf.garage();
  // const vitals = getVitalsData();
  // console.log(vitals);

  console.log('Done!');
})();
