import { server } from './constants';
import sleep from './sleep';
import { clientResponse, clientTask } from './teslatypes';

declare const GM_xmlhttpRequest: any; // To satisfy the type checker
const request = GM_xmlhttpRequest;

class Client {

  private request<T>(endpoint: string, data?: any): Promise<T> {
    return new Promise((resolve, reject) => {
      const reqobj: any = {};
      // {
      //   method: (data) ? "POST" : "GET",
      //   url: `${server}/${endpoint}`, // Change this to your actual local server URL
      //   nocache: true,
      //   headers: {
      //     "Content-Type": "application/json"
      //   },
      //   data: JSON.stringify(data),
      //   onload: function (response: any) {
      //     console.log(response.responseText);
      //     resolve(JSON.parse(response.responseText));
      //   },
      //   onerror: function (error: any) {
      //     console.error("Request failed", error);
      //     reject(error);
      //   }
      // };
      reqobj['method'] = (data) ? "POST" : "GET";
      reqobj['url'] = `${server}/${endpoint}`;
      reqobj['nocache'] = true;
      reqobj['headers'] = {};
      reqobj['headers']['Content-Type'] = "application/json";
      reqobj['data'] = JSON.stringify(data);
      reqobj['onload'] = function (response: any) {
        console.log(response.responseText);
        try {
          resolve(JSON.parse(response.responseText));
        }
        catch (e) {
          resolve(response.responseText);
        }
      };
      reqobj['onerror'] = function (error: any) {
        console.error("Request failed", error);
        reject(error);
      };
      console.log('Request:', reqobj);
      request(reqobj);
    });
  }

  async wait_online(): Promise<true> {
    while (true) {
      try {
        const response = await this.echo();
        if (response === 'Hello, World!') {
          return true;
        }
      } catch (e) {
      }
      await sleep(10000);
    }
  }


  async echo() {
    return await this.request<'Hello, world!' | any>('hello');
  }

  async postTbxToken(tbxToken: string) {
    const data: any = {};
    data['token'] = tbxToken;
    await this.request<clientResponse>(`tbx_token`, data);
  }

  async getTask(): Promise<clientTask> {
    return await this.request<clientTask>('task');
  }

  async postRequestAutorizationStatus(id: number, success: boolean, message = '') {
    const data: any = {};
    data['id'] = id;
    data['success'] = success;
    data['message'] = message;
    await this.request<clientResponse>('request_authorization_status', data);
  }

}

const client = new Client();
export default client;
