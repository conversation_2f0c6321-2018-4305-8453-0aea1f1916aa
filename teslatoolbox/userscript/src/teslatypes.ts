export type clientTask = {
  type: 'wait',
  seconds: number
} | {
  type: 'send_tbx_token',
} | {
  type: 'request_authorization',
  id: number,
  vin: string,
  tesla_mail: string,
};

export type clientResponse = {
  status: 'success',
} | {
  status: 'error',
  message: string,
};

export type job = {
  id: number;
  package_id: number;
  vehicle_id: number;
  state: string;
  created_at: number;
  updated_at: number;
  last_vehicle_activity: string | null;
  user_abandoned: boolean;
  vehicle_succeeded: boolean | null;
  owner: string;
  handshake_parameters: {
    wifi_wait_until: number;
    status_squelch?: string;
    use_hermes_stream?: string;
    download_retries?: number;
    modules_to_skip?: string;
    soc_check?: boolean;
    soc_threshold?: number;
    force_apeb?: boolean;
    verify_gwxfer_write?: string;
    mask_ui_notify?: string;
  };
  tag: string;
  issues: string | null;
  details: {
    reported_success_to_ui?: boolean;
  };
  late_failure_type: string | null;
  package: {
    id: number;
    name: string;
    signature: string;
    location: string;
    vehicle_hardware_configuration_id: number | null;
    created_at: number;
    updated_at: number;
    build_id: number;
    handshake_parameters: object | null;
    build_type: string;
    release_id: number;
    container: string;
    tegra_os_sha: string;
    md5: string;
    num_bytes: number;
    jenkins_job_name: string;
    cdn_path: string;
    crypto_block_size: number;
    can_decrypt_full_package: boolean;
    trial_expiration: number | null;
    secondary_package_sigs: string[] | null;
    artifacts: string | null;
    is_prod_signed: boolean;
    branch: string;
    signature_blob: string | null;
    region: string | null;
    signing_domain: string;
    build_flavor: string | null;
    is_ui_debug: boolean;
    is_ap_debug: boolean;
    release_scope: string | null;
    encrypted_md5: string;
    ape_decrypt: boolean;
    customer_version: string;
    usb_cdn_path: string;
    encrypted_location: string;
    odin_patch_id: number | null;
    encryption_key_id: string;
    state: string | null;
    zip_location: string | null;
    abandoned: boolean;
    non_deployable_commit_id: number | null;
    security_version: number | null;
    last_job_at: number;
    approval_manifest: string | null;
    validations: string | null;
    abandon_reason: string | null;
    device_allowlist: string | null;
    device_blocklist: string | null;
    ap_git_hash: string | null;
    metadata: object | null;
  };
};

export type release = {
  id: number;
  name: string;
  validated: boolean;
  abandoned: boolean;
  svn_path: null | string;
  svn_revision: null | string;
  created_at: number;
  updated_at: number;
  factory_default: boolean;
  whitelist: null | string;
  blacklist: null | string;
  non_deployable_commit_id: null | string;
};

export type firmware = {
  id: number;
  name: string;
  signature: string;
  vehicle_hardware_configuration_id: null | number;
  created_at: number;
  updated_at: number;
  build_id: number;
  handshake_parameters: null | object;
  build_type: string;
  release_id: number;
  container: string;
  tegra_os_sha: string;
  md5: string;
  num_bytes: number;
  jenkins_job_name: string;
  cdn_path: string;
  can_decrypt_full_package: boolean;
  trial_expiration: null | number;
  secondary_package_sigs: string[];
  artifacts: string;
  is_prod_signed: boolean;
  branch: string;
  signature_blob: null | string;
  region: null | string;
  signing_domain: string;
  build_flavor: string;
  is_ui_debug: boolean;
  is_ap_debug: boolean;
  release_scope: string;
  encrypted_md5: string;
  ape_decrypt: boolean;
  customer_version: string;
  usb_cdn_path: string;
  odin_patch_id: null | number;
  encryption_key_id: string;
  state: null | string;
  zip_location: string;
  abandoned: boolean;
  non_deployable_commit_id: null | number;
  security_version: number;
  last_job_at: number;
  approval_manifest: null | string;
  validations: null | string;
  abandon_reason: null | string;
  device_allowlist: null | string;
  device_blocklist: null | string;
  ap_git_hash: string;
  metadata: {
    ap_artifacts: null | string;
  };
  release: release;
  pre_signed: boolean;
  device_type: string;
  approver_details: any[];
  container_requires_signing_approval: boolean;
};

export type map = {
  id: number;
  name: string;
  signature: string;
  vehicle_hardware_configuration_id: null | number;
  created_at: number;
  updated_at: number;
  build_id: number;
  handshake_parameters: null | object;
  build_type: string;
  release_id: number;
  container: string;
  tegra_os_sha: string;
  md5: string;
  num_bytes: number;
  jenkins_job_name: string;
  cdn_path: string;
  can_decrypt_full_package: boolean;
  trial_expiration: null | number;
  secondary_package_sigs: null | string;
  artifacts: null | string;
  is_prod_signed: boolean;
  branch: string;
  signature_blob: string;
  region: string;
  signing_domain: string;
  build_flavor: null | string;
  is_ui_debug: boolean;
  is_ap_debug: boolean;
  release_scope: null | string;
  encrypted_md5: string;
  ape_decrypt: boolean;
  customer_version: string;
  usb_cdn_path: string;
  odin_patch_id: null | number;
  encryption_key_id: string;
  state: null | string;
  zip_location: null | string;
  abandoned: boolean;
  non_deployable_commit_id: null | string;
  security_version: null | number;
  last_job_at: number;
  approval_manifest: null | string;
  validations: null | string;
  abandon_reason: null | string;
  device_allowlist: null | string;
  device_blocklist: null | string;
  ap_git_hash: null | string;
  metadata: null | {
    ap_artifacts: null | string;
  };
  release: release;
  pre_signed: boolean;
  device_type: string;
  approver_details: any[];
  container_requires_signing_approval: boolean;
}

export type vitals_json = {
  "12VBatteryCurrent": number;
  "12VBatteryTemp": number;
  "12VBatteryVoltage": number;
  APP_decisionTemperature: string;
  APP_fisheyeCalibProgress: null | number;
  APP_fisheyeCalibrated: null | boolean;
  APP_fisheyeCamExtPitchCal: null | number;
  APP_fisheyeCamExtYawCal: null | number;
  APP_lPillarCalibProgress: null | number;
  APP_lPillarCalibrated: null | boolean;
  APP_lPillarCamExtPitchCal: null | number;
  APP_lPillarCamExtYawCal: null | number;
  APP_lRepeatCalibProgress: null | number;
  APP_lRepeatCalibrated: null | boolean;
  APP_lRepeatCamExtPitchCal: null | number;
  APP_lRepeatCamExtYawCal: null | number;
  APP_mainCalibProgress: null | number;
  APP_mainCalibrated: null | boolean;
  APP_mainCamExtPitchCal: null | number;
  APP_mainCamExtYawCal: null | number;
  APP_narrowCalibProgress: null | number;
  APP_narrowCalibrated: null | boolean;
  APP_narrowCamExtPitchCal: null | number;
  APP_narrowCamExtYawCal: null | number;
  APP_rPillarCalibProgress: null | number;
  APP_rPillarCalibrated: null | boolean;
  APP_rPillarCamExtPitchCal: null | number;
  APP_rPillarCamExtYawCal: null | number;
  APP_rRepeatCalibProgress: null | number;
  APP_rRepeatCalibrated: null | boolean;
  APP_rRepeatCamExtPitchCal: null | number;
  APP_rRepeatCamExtYawCal: null | number;
  APS_canMaster: string;
  USOE: number;
  VAPI_doorState: string;
  acc_rail: string;
  alert_data: [title: string, target: string, text: string, codes: string][];
  auto_front_door: string;
  auto_present_handles: string;
  bday: string;
  bms_cac_avg: string;
  bms_cac_max: string;
  bms_cac_min: string;
  bms_contactor_fc: string;
  bms_contactor_state: string;
  bms_current: number;
  bms_iso: string;
  bms_link_v: string;
  bms_max_temp: number;
  bms_min_temp: number;
  bms_pack_temp: number;
  bms_pack_temp_pct: number;
  bms_soc_max: string;
  bms_soc_min: string;
  bms_state: string;
  bms_voltage: number;
  carver: string;
  cd_display_state: number;
  cell_ant: string;
  cell_apn: string;
  cell_bars: number;
  cell_model: string;
  cell_ps_state: string;
  cell_reg: string;
  cell_sim_selection: string;
  cell_sys_mode: string;
  cfg_12v_battery_type: string;
  cfg_airsuspension: string;
  cfg_audiotype: string;
  cfg_autopilot: string;
  cfg_car: string;
  cfg_chargertype: string;
  cfg_connectivitypackage: string;
  cfg_country: string;
  cfg_driver_assist: string;
  cfg_exteriorcolor: string;
  cfg_fc_allowed: string;
  cfg_folding_mirrors: boolean;
  cfg_four_wheel_drive: boolean;
  cfg_frontfog: boolean;
  cfg_homelink: string;
  cfg_memorymirrors: boolean;
  cfg_navigationmapregion: string;
  cfg_other_fc_allowed: boolean;
  cfg_perf_add_on: string;
  cfg_powerliftgate: boolean;
  cfg_rear_drive_unit: string;
  cfg_rearfog: boolean;
  cfg_spoiler: string;
  cfg_sunroof: string;
  cfg_towing: string;
  cfg_tpms_type: string;
  cfg_wheeltype: string;
  cfg_xm_antenna: boolean;
  chg_av_power: null | number;
  chg_avail_master: number;
  chg_avail_slave: number;
  chg_avail_total: number;
  chg_cable_curr_limit: number;
  chg_current_master: number;
  chg_current_slave: number;
  chg_current_total: number;
  chg_enabled: boolean;
  chg_evse: number;
  chg_fault: boolean;
  chg_main_state: string;
  chg_p1_enabled: string;
  chg_p1_fault: string;
  chg_p1_linecurrent: string;
  chg_p1_mainstate: string;
  chg_p1_templega: string;
  chg_p1_templegb: string;
  chg_p1_vin: string;
  chg_p2_enabled: string;
  chg_p2_fault: string;
  chg_p2_linecurrent: string;
  chg_p2_mainstate: string;
  chg_p2_templega: string;
  chg_p2_templegb: string;
  chg_p2_vin: string;
  chg_p3_enabled: string;
  chg_p3_fault: string;
  chg_p3_linecurrent: string;
  chg_p3_mainstate: string;
  chg_p3_templega: string;
  chg_p3_templegb: string;
  chg_p3_vin: string;
  chg_phases: null | number;
  chg_pilot: string;
  chg_pilot_c: number;
  chg_prox: string;
  chg_voltage_master: number;
  chg_voltage_slave: number;
  chgs_av_power: null | number;
  chgs_cable_curr_limit: number;
  chgs_enabled: boolean;
  chgs_fault: boolean;
  chgs_main_state: string;
  chgs_p1_enabled: string;
  chgs_p1_fault: string;
  chgs_p1_linecurrent: string;
  chgs_p1_mainstate: string;
  chgs_p1_templega: string;
  chgs_p1_templegb: string;
  chgs_p1_vin: string;
  chgs_p2_enabled: string;
  chgs_p2_fault: string;
  chgs_p2_linecurrent: string;
  chgs_p2_mainstate: string;
  chgs_p2_templega: string;
  chgs_p2_templegb: string;
  chgs_p2_vin: string;
  chgs_p3_enabled: string;
  chgs_p3_fault: string;
  chgs_p3_linecurrent: string;
  chgs_p3_mainstate: string;
  chgs_p3_templega: string;
  chgs_p3_templegb: string;
  chgs_p3_vin: string;
  child_lock: string;
  cp_cover_closed: boolean;
  cp_door: boolean;
  cp_door_sensor: boolean;
  cp_insert_enable: boolean;
  cp_latch: string;
  cp_led_color: string;
  cp_type: string;
  door_unlock_mode: string;
  drive_rail: string;
  gear: null | number;
  gps_ant: string;
  gps_fix_type: number;
  hvac_rail: string;
  ic_display_state: number;
  info_fuse_state: string;
  info_hw: string;
  is_locked: boolean;
  key_fob_close_all: string;
  language: string;
  max_brick_volt: number;
  min_brick_volt: number;
  nav_map_release: string;
  odo: number;
  outside_temp: null | number;
  passive_entry_enabled: string;
  pt_in_act_t: null | number;
  pt_in_pass_t: null | number;
  range: number;
  recent_alerts: string[];
  remote_service_alert: string;
  sats_in_use: number;
  showroom_mode: boolean;
  speed: null | number;
  thc_aux_evap_a: string;
  thc_aux_evap_sol: string;
  thc_aux_evap_t: string;
  thc_battpump1_req: null | number;
  thc_battpump2_req: null | number;
  thc_chill_onoff: null | boolean;
  thc_comp_rpm: null | number;
  thc_disch_pres: null | number;
  thc_disch_pres_target: null | number;
  thc_disch_t: null | number;
  thc_hvac_onoff: null | boolean;
  thc_pt_in: null | number;
  thc_ptpump2_req: string;
  thc_ptpump_req: null | number;
  thc_suct_pres: null | number;
  thc_suct_t: null | number;
  time: string;
  tpms_pressure_fl: string;
  tpms_pressure_fr: string;
  tpms_pressure_rl: string;
  tpms_pressure_rr: string;
  ui_mode: string;
  unlock_on_park: string;
  utcoffset: number;
  valid_gps: boolean;
  vin: string;
  walk_away_lock: string;
  wifi_address: string;
  wifi_bars: number;
  wifi_cellstate: null | number;
  wifi_connstatus: string;
  wifi_enabled: boolean;
  wifi_last_up_time_epoch: number;
  wifi_linkstate: string;
  wifi_signal: number;
  wifi_wifistate: string;
  fw_package: firmware;
  map_package: map;
  latest_fw: firmware;
  latest_map: map;
  job_history: job[];
  active_jobs: job[];
};
