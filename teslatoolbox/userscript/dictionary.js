const dictionary = [
  'a',
  'b',
  'c',
  'd',
  'e',
  'f',
  'g',
  'h',
  'i',
  'j',
  'k',
  'l',
  'm',
  'n',
  'o',
  'p',
  'q',
  'r',
  's',
  't',
  'u',
  'v',
  'w',
  'x',
  'y',
  'z',
  'lll',
  'll1',
  'lli',
  'l1l',
  'l11',
  'l1i',
  'lil',
  'li1',
  'lii',
  'ill',
  'il1',
  'ili',
  'i1l',
  'i11',
  'i1i',
  'iil',
  'ii1',
  'iii',

  'llii1lll',
  'llii1ll1',
  'llii1lli',
  'llii1l1l',
  'llii1l11',
  'llii1l1i',
  'llii1lil',
  'llii1li1',
  'llii1lii',
  'ilii1lll',
  'ilii1ll1',
  'ilii1lli',
  'ilii1l1l',
  'ilii1l11',
  'ilii1l1i',
  'ilii1lil',
  'ilii1li1',
  'ilii1lii',

  'llilll1lll',
  'llilll1ll1',
  'llilll1lli',
  'llilll1l1l',
  'llilll1l11',
  'llilll1l1i',
  'llilll1lil',
  'llilll1li1',
  'llilll1lii',
  'ililll1lll',
  'ililll1ll1',
  'ililll1lli',
  'ililll1l1l',
  'ililll1l11',
  'ililll1l1i',
  'ililll1lil',
  'ililll1li1',
  'ililll1lii',
  // 'electrify',
  'europe',
  'user',
  // 'password',
  'login',
  'logout',
  'garage',
  'car',
  'battery',
  'voltage',
  'BMS',
  'CAN',
  'tire',
  'VIN',
  'fc',
  'charge',
  'motor',
  'pack',
  'EV',
  'amp',
  'token',
  'range',
  'port',
  'OTA',
  'Tesla',
  // 'autopilot',
  'HVAC',
  'ACC',
  'rail',
  'utc',
  'jobs',
  'id',
  'alert',
  'alerts',
  'service',
  'fix',
  'door',
  'window',
  'locked',
  'VCU',
  'MCU',
  'PCB',
  'nav',
  'GPS',
  'TPMS',
  // 'software',
  'sensor',
  'cac',
  'AI',
  'IoT',
  'api',
  'wifi',
  // 'connect',
  // 'connection',
  // 'disconnect',
  // 'country',
  // 'language',
  // 'unlock',
  'key',
  'fob',
  'type',
  'fuse',
  'relay',
  'wire',
  'plug',
  // 'DC',
  // 'AC',
  // 'DTC',
  // 'regen',
  // 'coolant',
  // 'contactor',
  // 'anode',
  // 'cathode',
  // 'fast',
  // 'slow',
  // 'station',
  // 'OBD',
  // 'Leaf',
  // 'hybrid',
  // 'brick',
  // 'Ah',
  // 'kWh',
  // 'Ohm',
  // 'torque',
  // 'boost',
  // 'efficiency',
  // 'diagnostic',
  // 'thermal',
  // 'fault',
  // 'lithium',
  // 'auto',
  // 'manual',
  // 'pilot',
  // 'front',
  // 'rear',
  // 'left',
  // 'right',
  // 'ion',
  // 'NMC',
  // 'LFP',
  // 'cycle',
  // 'cyclic',
  // 'discharge',
  // 'chemistry',
  // 'energy',
  // 'power',
  // 'stator',
  // 'rotor',
  // 'heat',
  // 'driver',
  // 'owner',
  // 'mirror',
  // 'insulation',
  // 'drivetrain',
  // 'chassis',
  // 'ECU',
  // 'cooling',
  // 'hydrogen',
  // 'supercap',
  // 'ultracap',
  // 'charger',
  // 'audio',
  // 'smart',
  // 'controller',
  // 'mileage',
  // 'warranty',
  // 'degrade',
  // 'repair',
  // 'replace',
  // 'replacement',
  // 'module',
  // 'safety',
  // 'hazard',
  // 'electrolyte',
  // 'testing',
  // 'breakdown',
  // 'resistor',
  // 'transducer',
  // 'maintenance',
  // 'brushless',
  // 'magnet',
  // 'dashboard',
  // 'firmware',
  // 'server',
  // 'client',
  // 'cloud',
  // 'http',
  // 'update',
  // 'overheat',
  // 'diagnostics',
  // 'SOH',
  // 'balance',
  // 'telemetry',
  // 'onboard',
  // 'traction',
  // 'connectivity',
  // 'wireless',
  // 'protocol',
  // 'SOC',
  // 'EMS',
  // 'RPM',
  // 'FWD',
  // 'RWD',
  // 'AWD',
  // 'capacity',
  // 'throttle',
  // 'EVSE',
  // 'PID',
  // 'logger',
  // 'drive',
  // 'log',
  // 'adapter',
  // 'data',
  // 'shutdown',
  // 'idle',
  // 'ampere',
  // 'stack',
  // 'current',
  // 'short',
  // 'surge',
  // 'bus',
  // 'inverter',
  // 'leakage',
  // 'impedance',
  // 'spectrum',
  // 'load',
  // 'phase',
  // 'optimization',
  // 'diagnose',
  // 'electrolysis',
  // 'chargeport',
  // 'grid',
  // 'utility',
  // 'harmonic',
  // 'degradation',
  // 'temperature',
  // 'enclosure',
  // 'prismatic',
  // 'pouch',
  // 'cylindrical',
  // 'overshoot',
  // 'ripple',
  // 'sine',
  // 'waveform',
  // 'signal',
  // 'low',
  // 'high',
  // 'cell',
  // 'millivolt',
  // 'kilowatt',
  // 'shunt',
  // 'electrode',
  // 'iso',
  // 'isolator',
  // 'grounding',
  // 'shield',
  // 'spike',
  // 'remote',
  // 'control',
  // 'brake',
  // 'pedal',
];

module.exports = dictionary;
