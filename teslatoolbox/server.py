import time
from pprint import pformat

from flask import Flask, abort, request, send_from_directory
from flask_cors import CORS
from sqlalchemy import or_, select
from werkzeug.middleware.proxy_fix import ProxyFix

from carpass import carpass_blueprint
from microsoft import microsoft_blueprint
from teslatoolbox.tesla_api.bearer import has_bearer, update_bearer
from teslatoolbox.db import Customer, CustomerConfirmation, Diagnosis, DiagnosisState, DiagnosisType, OAuthToken, get_db_session, may_retry
from teslatoolbox.utils.getenv import env
from teslatoolbox.utils.logger import logger
from teslatoolbox.pdfreport import create_pdf_report

app = Flask(__name__)
if env == 'production':
    app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_prefix=1)
CORS(app)

app.register_blueprint(carpass_blueprint, url_prefix='/carpass')
app.register_blueprint(microsoft_blueprint, url_prefix='/microsoft')


@app.route('/script.user.js')
def serve_inject_user_js():
    return send_from_directory('userscript/dist', 'script.user.js')


@app.route('/script.obvs.user.js')
def serve_userscript_js():
    return send_from_directory('userscript/dist', 'script.obvs.user.js')


@app.route('/task')
def get_task():
    tasks = [
        task_send_tbx_token,
    ]
    for task in tasks:
        result = task()
        if result is not None:
            return result
    return {'type': 'wait', 'seconds': 120}


def task_send_tbx_token():
    """Returns data for the client if this task is needed, None otherwise."""
    if not has_bearer():
        return {'type': 'send_tbx_token'}
    return None


@app.route('/tbx_token', methods=['POST'])
def tbx_token():
    # Get the token from the request
    if not (request.json and 'token' in request.json):
        return {'status': 'error', 'message': 'Invalid request'}, 400
    token = request.json['token']
    update_bearer(token)
    return {'status': 'success'}


@app.route('/request_diagnosis', methods=['POST'])
def request_diagnosis():
    if not request.json:
        abort(401)
    if not ('vin' in request.json and 'tesla_mail' in request.json and 'type' in request.json and 'token' in request.json):
        abort(401)
    if not str(request.json['vin']).strip().isalnum():
        return {'status': 'error', 'message': 'Invalid VIN'}, 400
    if not str(request.json['tesla_mail']).find('@') > 0:
        return {'status': 'error', 'message': 'Invalid Tesla mail'}, 400
    vin = str(request.json['vin']).strip().upper()
    tesla_mail = str(request.json['tesla_mail']).strip()
    diagnosis_type = str(request.json['type'])
    token = str(request.json['token'])

    if not (diagnosis_type in DiagnosisType and token == 'e0y9hRo0AC730fAEvBlg7zPHa27MbE'):
        abort(401)

    with get_db_session() as db:
        customer = Customer(vin=vin, tesla_mail=tesla_mail)
        db.add(customer)
        diagnosis = Diagnosis(customer=customer, requested_at=int(time.time()), state=DiagnosisState.INIT, diagnosis_type=DiagnosisType(diagnosis_type))
        db.add(diagnosis)
        db.commit()

    return {'status': 'success'}


@app.route('/overview')
def overview():
    token = request.args.get('token')
    if not token == 'very_secret':
        abort(401)
    html = '<html><head><title>Overview</title><style>td{border-top:1px solid grey;}</style></head><body><table>'
    html += '<tr><th>ID</th><th>VIN</th><th>Tesla Mail</th><th>Requested At</th><th>State</th><th>Retry After</th><th>Vitals</th><th>Report</th><th>Error / Message</th></tr>'
    with get_db_session() as db:
        diagnoses = db.scalars(select(Diagnosis))
        for diagnosis in diagnoses:
            html += f'<tr><td><a href="report?id={diagnosis.id}">{diagnosis.id}</a></td><td>{diagnosis.customer.vin}</td><td>{diagnosis.customer.tesla_mail}</td><td>{diagnosis.requested_at}</td><td>{diagnosis.state.value}</td><td>{diagnosis.retry_after}</td><td><pre>{pformat(diagnosis.vitals)}<pre></td><td><pre>{pformat(diagnosis.report)}</pre></td><td>{diagnosis.error_message}</td></tr>'
    html += '</table></body></html>'
    return html


@app.route('/oauth_overview')
def oauth_overview():
    token = request.args.get('token')
    if not token == 'in_my_mind':
        abort(401)
    html = '<html><head><title>OAuth Overview</title></head><body><table>'
    html += '<tr><th>ID</th><th>Mail</th><th>Provider</th><th>Updated At</th><th>Expires At</th><th>Raw Data</th><th>User Data</th></tr>'
    with get_db_session() as db:
        tokens = db.scalars(select(OAuthToken))
        for token in tokens:
            html += f'<tr><td>{token.id}</td><td>{token.user_mail}</td><td>{token.provider.value}</td><td>{token.updated_at}</td><td>{token.expires_at}</td><td><pre>{pformat(token.raw_data)}<pre></td><td><pre>{pformat(token.user_data)}</pre></td></tr>'
    html += '</table></body></html>'
    return html


@app.route('/report')
def report():
    id = request.args.get('id')
    if not (id and id.isnumeric()):
        return 'Invalid ID', 400
    with get_db_session() as db:
        diagnosis = db.scalar(select(Diagnosis).where(Diagnosis.id == int(id)))
        if diagnosis is None:
            return 'Diagnosis not found', 404
        if diagnosis.report is None:
            return 'No report available', 404
        pdf_bytes = create_pdf_report(diagnosis)
        if pdf_bytes is None:
            return 'Error creating report', 500
    return pdf_bytes, 200, {'Content-Type': 'application/pdf', 'Content-Disposition': f'inline; filename="report {diagnosis.customer.vin}.pdf"'}


@app.route('/sample_report')
def sample_report():
    return send_from_directory('static', 'free_report_template.html')


@app.route('/sample_report_pdf')
def sample_report_pdf():
    return send_from_directory('static', 'sample_report.pdf')


@app.route('/hello')
def hello():
    """Used to check if the server is running by the userscript."""
    return 'Hello, World!'


if __name__ == '__main__':
    assert env == 'development'
    app.run(port=8001, debug=True)  # nginx ssh on port 8002 is mapped to port 8001

    # Run the server with the following command:
    # python -m teslatoolbox.server

    # Production:
    # gunicorn -w 4 -b 0.0.0.0:8000 teslatoolbox.server:app
