from functools import cache
import time

from jinja2 import Template
from weasyprint import HTML

from teslatoolbox.utils.logger import logger
from teslatoolbox.db import Diagnosis


def create_pdf_report(diagnosis: Diagnosis):
    html = create_html_report(diagnosis)
    if html is None:
        return
    return HTML(string=html).write_pdf()


# @cache
def get_template():
    with open('teslatoolbox/static/free_report_template.html') as f:
        return f.read()


def create_html_report(diagnosis: Diagnosis):
    template = get_template()
    try:
        data = {
            'vin': diagnosis.customer.vin,
            'model': diagnosis.vitals['model'],
            'color': diagnosis.vitals['color'],
            'birthday': time.strftime('%d %B %Y', tuple(diagnosis.vitals['birthday'])),
            'odometer': int(diagnosis.vitals['odometer']),
            'now': time.strftime('%d %B %Y'),
            'dataTime': time.strftime('%d %B %Y %H:%M:%S', time.localtime(diagnosis.vitals['time'])),
            'deltaV': diagnosis.report['deltaV']['value'],
            'amountFastCharging': diagnosis.report['amountFastCharging']['value'],
        }

        moisture_value = diagnosis.report['isolationGrade']['value']
        if diagnosis.vitals['model'][0] in ['S', 'X']:
            data['moisture'] = 'dry' if moisture_value > 3600 else 'condensation' if moisture_value > 3200 else 'wet'
        elif diagnosis.vitals['model'][0] in ['3', 'Y']:
            data['moisture'] = 'dry' if moisture_value > 4000 else 'wet'
        else:
            raise Exception(f'Unknown model: {diagnosis.vitals["model"]}')

    except Exception as e:
        logger.error(f'[{diagnosis.customer.vin}] Error gathering report data: {e}')
        return None

    html = Template(template).render(data)
    return html
