import time

import microsoft.oauth2
from teslatoolbox.tesla_api.bearer import has_bearer, remove_bearer
from teslatoolbox.utils.getenv import env
from teslatoolbox.utils.logger import logger
from teslatoolbox.services.diagnose import diagnose_all
from teslatoolbox.services.get_vehicle_id import get_all_vehicle_ids
from teslatoolbox.services.mail_report import send_all_reports
from teslatoolbox.services.mail_wrong_details import send_all_wrong_mail_mails, send_all_wrong_vin_mails
from teslatoolbox.services.request_authorization import request_all_authorizations
from teslatoolbox.services.request_logs import request_all_logs
from teslatoolbox.services.update_auth_status import update_all_auth_status
from teslatoolbox.services.wait_for_logs import wait_for_logs
from teslatoolbox.tesla_api.tesla_types import TokenExpiredError


def main():
    print_bearer_wait_message = True

    while True:
        if not has_bearer():
            if print_bearer_wait_message:
                print_bearer_wait_message = False
                logger.info('Waiting for bearer token...')
        else:
            if not print_bearer_wait_message:
                print_bearer_wait_message = True
                logger.info('Bearer token received.')

            try:
                # INIT → AUTHORIZATION_PENDING
                request_all_authorizations()

                # AUTHORIZATION_PENDING → AUTHORIZATION_APPROVED
                update_all_auth_status()

                # GETTING_VEHICLE_ID → GOT_VEHICLE_ID
                get_all_vehicle_ids()

                # REQUESTING_LOGS → LOGS_REQUESTED
                request_all_logs()

                # WAITING_FOR_LOGS → LOGS_ADEQUATE
                wait_for_logs()

                # DIAGNOSING → DIAGNOSED
                diagnose_all()

            except TokenExpiredError:
                logger.warning('Token expired error.')
                remove_bearer()

        if env == 'production':
            # SENDING_REPORT → DONE
            microsoft.oauth2.refresh_tokens()
            send_all_reports()

            # Send mails to everyone who has entered wrong details
            send_all_wrong_vin_mails()
            send_all_wrong_mail_mails()

        time.sleep(254)


if __name__ == '__main__':
    main()
